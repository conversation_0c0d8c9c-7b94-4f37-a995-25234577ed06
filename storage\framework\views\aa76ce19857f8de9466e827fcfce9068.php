<?php if (isset($component)) { $__componentOriginalc7b6b0f5d16f8a605a52718a155b5029 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc7b6b0f5d16f8a605a52718a155b5029 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layouts.install','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('layouts.install'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('title', null, []); ?> 
        <?php echo e(trans('install.steps.database')); ?>

     <?php $__env->endSlot(); ?>

     <?php $__env->slot('content', null, []); ?> 
        <div class="grid sm:grid-cols-6 gap-x-8 gap-y-6 my-3.5">
            <?php if (isset($component)) { $__componentOriginal8a7c1d40ad1ae10d549822cf310c828e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8a7c1d40ad1ae10d549822cf310c828e = $attributes; } ?>
<?php $component = App\View\Components\Form\Group\Text::resolve(['name' => 'hostname','label' => ''.e(trans('install.database.hostname')).'','value' => ''.e(old('hostname', $host)).'','formGroupClass' => 'sm:col-span-6'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.group.text'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\Form\Group\Text::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8a7c1d40ad1ae10d549822cf310c828e)): ?>
<?php $attributes = $__attributesOriginal8a7c1d40ad1ae10d549822cf310c828e; ?>
<?php unset($__attributesOriginal8a7c1d40ad1ae10d549822cf310c828e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8a7c1d40ad1ae10d549822cf310c828e)): ?>
<?php $component = $__componentOriginal8a7c1d40ad1ae10d549822cf310c828e; ?>
<?php unset($__componentOriginal8a7c1d40ad1ae10d549822cf310c828e); ?>
<?php endif; ?>

            <?php if (isset($component)) { $__componentOriginal8a7c1d40ad1ae10d549822cf310c828e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8a7c1d40ad1ae10d549822cf310c828e = $attributes; } ?>
<?php $component = App\View\Components\Form\Group\Text::resolve(['name' => 'username','label' => ''.e(trans('install.database.username')).'','value' => ''.e(old('username', $username)).'','formGroupClass' => 'sm:col-span-6'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.group.text'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\Form\Group\Text::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8a7c1d40ad1ae10d549822cf310c828e)): ?>
<?php $attributes = $__attributesOriginal8a7c1d40ad1ae10d549822cf310c828e; ?>
<?php unset($__attributesOriginal8a7c1d40ad1ae10d549822cf310c828e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8a7c1d40ad1ae10d549822cf310c828e)): ?>
<?php $component = $__componentOriginal8a7c1d40ad1ae10d549822cf310c828e; ?>
<?php unset($__componentOriginal8a7c1d40ad1ae10d549822cf310c828e); ?>
<?php endif; ?>

            <?php if (isset($component)) { $__componentOriginalbef53af98b221ad245a9efde80170a7e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalbef53af98b221ad245a9efde80170a7e = $attributes; } ?>
<?php $component = App\View\Components\Form\Group\Password::resolve(['name' => 'password','label' => ''.e(trans('install.database.password')).'','value' => ''.e($password).'','notRequired' => true,'formGroupClass' => 'sm:col-span-6'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.group.password'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\Form\Group\Password::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalbef53af98b221ad245a9efde80170a7e)): ?>
<?php $attributes = $__attributesOriginalbef53af98b221ad245a9efde80170a7e; ?>
<?php unset($__attributesOriginalbef53af98b221ad245a9efde80170a7e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalbef53af98b221ad245a9efde80170a7e)): ?>
<?php $component = $__componentOriginalbef53af98b221ad245a9efde80170a7e; ?>
<?php unset($__componentOriginalbef53af98b221ad245a9efde80170a7e); ?>
<?php endif; ?>

            <?php if (isset($component)) { $__componentOriginal8a7c1d40ad1ae10d549822cf310c828e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8a7c1d40ad1ae10d549822cf310c828e = $attributes; } ?>
<?php $component = App\View\Components\Form\Group\Text::resolve(['name' => 'database','label' => ''.e(trans('install.database.name')).'','value' => ''.e(old('database', $database)).'','formGroupClass' => 'sm:col-span-6'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.group.text'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\Form\Group\Text::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8a7c1d40ad1ae10d549822cf310c828e)): ?>
<?php $attributes = $__attributesOriginal8a7c1d40ad1ae10d549822cf310c828e; ?>
<?php unset($__attributesOriginal8a7c1d40ad1ae10d549822cf310c828e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8a7c1d40ad1ae10d549822cf310c828e)): ?>
<?php $component = $__componentOriginal8a7c1d40ad1ae10d549822cf310c828e; ?>
<?php unset($__componentOriginal8a7c1d40ad1ae10d549822cf310c828e); ?>
<?php endif; ?>
        </div>
     <?php $__env->endSlot(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc7b6b0f5d16f8a605a52718a155b5029)): ?>
<?php $attributes = $__attributesOriginalc7b6b0f5d16f8a605a52718a155b5029; ?>
<?php unset($__attributesOriginalc7b6b0f5d16f8a605a52718a155b5029); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc7b6b0f5d16f8a605a52718a155b5029)): ?>
<?php $component = $__componentOriginalc7b6b0f5d16f8a605a52718a155b5029; ?>
<?php unset($__componentOriginalc7b6b0f5d16f8a605a52718a155b5029); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Desktop\akaunting\resources\views/install/database/create.blade.php ENDPATH**/ ?>