name: Translations

on:
  schedule:
    - cron: '0 0 * * *'
  workflow_dispatch:

jobs:
  sync:
    name: Sync

    runs-on: ubuntu-latest

    steps:

    - name: Checkout
      uses: actions/checkout@v4

    - name: Sync with <PERSON><PERSON>
      uses: crowdin/github-action@master
      with:
        upload_sources: true
        upload_translations: true
        download_translations: true
        skip_untranslated_files: true

        source: 'resources/lang/en-GB/*.php'
        translation: 'resources/lang/%locale%/%original_file_name%'

        localization_branch_name: 'translations'
        commit_message: 'new crowdin translations'
        pull_request_title: 'New Crowdin translations'
        pull_request_body: 'https://crowdin.com/project/akaunting'
        pull_request_labels: 'Translation'
        
        project_id: ${{ secrets.CROWDIN_CORE_ID }}
        token: ${{ secrets.CROWDIN_PERSONAL_TOKEN }}
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
