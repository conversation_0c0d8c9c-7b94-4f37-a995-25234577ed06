# Akaunting Installation Guide

## 🎯 Quick Start

**For the fastest setup, run this as Administrator:**
```batch
install-akaunting.bat
```

This master script will handle everything automatically!

## 📁 Files Created

| File | Purpose |
|------|---------|
| `install-akaunting.bat` | **Master installer** - Runs everything automatically |
| `setup-akaunting.bat` | Main Akaunting setup script |
| `install-xampp.bat` | XAMPP installation script |
| `test-setup.bat` | System requirements tester |
| `.env` | Environment configuration (pre-configured) |
| `SETUP_GUIDE.md` | Detailed manual installation guide |

## 🚀 Installation Options

### Option 1: Automatic Installation (Recommended)
```batch
# Right-click and "Run as Administrator"
install-akaunting.bat
```

### Option 2: Step-by-Step Installation
```batch
# 1. Test system requirements
test-setup.bat

# 2. Install XAMPP
install-xampp.bat

# 3. Install Composer (run the downloaded installer)
composer-setup.exe

# 4. Set up Akaunting
setup-akaunting.bat
```

### Option 3: Manual Installation
Follow the detailed guide in `SETUP_GUIDE.md`

## ✅ What Each Script Does

### `install-akaunting.bat` (<PERSON> Script)
- ✅ Checks system requirements
- ✅ Installs XAMPP (Apache, MySQL, PHP 8.1+)
- ✅ Installs Composer
- ✅ Sets up Akaunting environment
- ✅ Installs PHP dependencies
- ✅ Configures database settings
- ✅ Creates necessary directories
- ✅ Opens XAMPP Control Panel

### `test-setup.bat` (Requirements Checker)
- ✅ Verifies all required files exist
- ✅ Checks directory structure
- ✅ Validates configuration files
- ✅ Tests system compatibility
- ✅ Provides detailed test results

### `setup-akaunting.bat` (Main Setup)
- ✅ Copies files to XAMPP htdocs
- ✅ Installs Composer dependencies
- ✅ Generates Laravel application key
- ✅ Sets up storage directories
- ✅ Configures environment variables
- ✅ Checks service status

## 🔧 System Requirements

- ✅ Windows 11 (tested and verified)
- ✅ Administrator privileges
- ✅ Internet connection
- ✅ At least 2GB free disk space

## 📋 Pre-Installation Checklist

Run `test-setup.bat` to verify:
- [x] Akaunting source code present
- [x] Environment configuration ready
- [x] Laravel framework detected
- [x] Required directories exist
- [x] Composer installer available
- [x] System compatibility confirmed

## 🎯 Post-Installation Steps

After running the installation scripts:

1. **Start Services**
   - Open XAMPP Control Panel
   - Start Apache and MySQL

2. **Create Database**
   - Open http://localhost/phpmyadmin
   - Create database named `akaunting`

3. **Run Migrations**
   ```batch
   cd C:\xampp\htdocs\akaunting
   php artisan migrate --seed
   ```

4. **Launch Akaunting**
   - Open http://localhost/akaunting
   - Complete the web setup wizard

## 🔍 Verification Commands

Test your installation:
```batch
# Check PHP
php --version

# Check Composer
composer --version

# Check Akaunting
cd C:\xampp\htdocs\akaunting
php artisan --version
```

## 🚨 Troubleshooting

### Common Issues:

**"Access Denied" Errors**
- Solution: Run scripts as Administrator

**"PHP not found"**
- Solution: Restart command prompt after XAMPP installation

**"Composer not found"**
- Solution: Restart command prompt after Composer installation

**"Database connection failed"**
- Solution: Start MySQL in XAMPP Control Panel

**"Port 80 already in use"**
- Solution: Stop IIS or change Apache port in XAMPP

### Getting Help:
1. Check the detailed logs in each script
2. Review `SETUP_GUIDE.md` for manual steps
3. Visit https://akaunting.com/support

## 🎉 Success Indicators

You'll know the installation worked when:
- ✅ XAMPP Control Panel opens automatically
- ✅ Apache and MySQL services start successfully
- ✅ http://localhost/akaunting loads the setup wizard
- ✅ No error messages in the installation scripts

## 📞 Support

- **Akaunting Documentation**: https://akaunting.com/docs
- **XAMPP Documentation**: https://www.apachefriends.org/faq.html
- **Laravel Documentation**: https://laravel.com/docs

---

**Ready to install? Run `install-akaunting.bat` as Administrator!**
