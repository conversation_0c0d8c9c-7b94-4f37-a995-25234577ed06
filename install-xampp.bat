@echo off
setlocal enabledelayedexpansion
color 0B

echo.
echo ========================================================
echo              XAMPP INSTALLER SCRIPT
echo ========================================================
echo.

:: Check if running as administrator
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] This script requires administrator privileges!
    echo [ERROR] Please right-click and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

echo [SUCCESS] Running with administrator privileges
echo.

:: Check if XAMPP is already installed
if exist "C:\xampp" (
    echo [INFO] XAMPP appears to be already installed at C:\xampp
    echo [INFO] Do you want to continue anyway? (Y/N)
    set /p "continue=Enter choice: "
    if /i "!continue!" neq "Y" (
        echo [INFO] Installation cancelled
        pause
        exit /b 0
    )
)

:: Check if winget is available
echo [INFO] Checking for Windows Package Manager (winget)...
winget --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Windows Package Manager (winget) is not available
    echo [INFO] Please install XAMPP manually from: https://www.apachefriends.org/
    pause
    exit /b 1
)

echo [SUCCESS] Windows Package Manager found
winget --version
echo.

:: Search for XAMPP packages
echo [INFO] Searching for XAMPP packages...
winget search xampp

echo.
echo [INFO] Attempting to install XAMPP using winget...
echo [INFO] This may take several minutes...
echo.

:: Try to install XAMPP
winget install --id=ApacheFriends.Xampp --silent --accept-package-agreements --accept-source-agreements

if %errorlevel% equ 0 (
    echo.
    echo [SUCCESS] XAMPP installation completed!
    echo.
) else (
    echo.
    echo [WARNING] winget installation may have failed or been cancelled
    echo [INFO] Trying alternative installation method...
    echo.
    
    :: Alternative: Download and install manually
    echo [INFO] Downloading XAMPP installer...
    powershell -Command "Invoke-WebRequest -Uri 'https://sourceforge.net/projects/xampp/files/XAMPP%%20Windows/8.2.12/xampp-windows-x64-8.2.12-0-VS16-installer.exe/download' -OutFile 'xampp-installer.exe'"
    
    if exist "xampp-installer.exe" (
        echo [INFO] Running XAMPP installer...
        start /wait xampp-installer.exe --mode unattended --launchapps 0 --installdir C:\xampp
        
        if exist "C:\xampp" (
            echo [SUCCESS] XAMPP installed successfully!
            del xampp-installer.exe
        ) else (
            echo [ERROR] XAMPP installation failed
            echo [INFO] Please install manually from: https://www.apachefriends.org/
        )
    ) else (
        echo [ERROR] Failed to download XAMPP installer
        echo [INFO] Please install manually from: https://www.apachefriends.org/
    )
)

:: Verify installation
echo.
echo [INFO] Verifying XAMPP installation...
if exist "C:\xampp\php\php.exe" (
    echo [SUCCESS] PHP found: C:\xampp\php\php.exe
    C:\xampp\php\php.exe --version
) else (
    echo [ERROR] PHP not found in XAMPP installation
)

if exist "C:\xampp\mysql\bin\mysql.exe" (
    echo [SUCCESS] MySQL found: C:\xampp\mysql\bin\mysql.exe
) else (
    echo [ERROR] MySQL not found in XAMPP installation
)

if exist "C:\xampp\apache\bin\httpd.exe" (
    echo [SUCCESS] Apache found: C:\xampp\apache\bin\httpd.exe
) else (
    echo [ERROR] Apache not found in XAMPP installation
)

:: Add PHP to PATH
echo.
echo [INFO] Adding PHP to system PATH...
set "currentPath="
for /f "tokens=2*" %%a in ('reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH 2^>nul') do set "currentPath=%%b"

echo !currentPath! | findstr /C:"C:\xampp\php" >nul
if !errorlevel! neq 0 (
    reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH /t REG_EXPAND_SZ /d "!currentPath!;C:\xampp\php" /f >nul
    echo [SUCCESS] PHP added to system PATH
    echo [INFO] Please restart your command prompt to use PHP
) else (
    echo [INFO] PHP already in system PATH
)

echo.
echo ========================================================
echo                INSTALLATION COMPLETE
echo ========================================================
echo.
echo NEXT STEPS:
echo 1. Restart your command prompt/PowerShell
echo 2. Test PHP: php --version
echo 3. Install Composer if not already installed
echo 4. Run the Akaunting setup script: setup-akaunting.bat
echo.
echo XAMPP CONTROL PANEL:
echo - Location: C:\xampp\xampp-control.exe
echo - Use it to start/stop Apache and MySQL services
echo.

if exist "C:\xampp\xampp-control.exe" (
    echo [INFO] Opening XAMPP Control Panel...
    start "" "C:\xampp\xampp-control.exe"
)

echo.
pause
