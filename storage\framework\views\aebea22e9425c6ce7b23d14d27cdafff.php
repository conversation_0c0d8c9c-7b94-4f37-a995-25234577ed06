<?php if (isset($component)) { $__componentOriginalc7b6b0f5d16f8a605a52718a155b5029 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc7b6b0f5d16f8a605a52718a155b5029 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layouts.install','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('layouts.install'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('title', null, []); ?> 
        <?php echo e(trans('install.steps.requirements')); ?>

     <?php $__env->endSlot(); ?>

     <?php $__env->slot('content', null, []); ?>  <?php $__env->endSlot(); ?>

    <?php $__env->startPush('scripts_start'); ?>
    <script type="text/javascript">
        var flash_requirements = <?php echo ($requirements) ? json_encode($requirements) : '[]'; ?>;
    </script>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc7b6b0f5d16f8a605a52718a155b5029)): ?>
<?php $attributes = $__attributesOriginalc7b6b0f5d16f8a605a52718a155b5029; ?>
<?php unset($__attributesOriginalc7b6b0f5d16f8a605a52718a155b5029); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc7b6b0f5d16f8a605a52718a155b5029)): ?>
<?php $component = $__componentOriginalc7b6b0f5d16f8a605a52718a155b5029; ?>
<?php unset($__componentOriginalc7b6b0f5d16f8a605a52718a155b5029); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Desktop\akaunting\resources\views/install/requirements/show.blade.php ENDPATH**/ ?>