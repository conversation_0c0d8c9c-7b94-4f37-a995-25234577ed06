@echo off
echo ===================================
echo Akaunting Setup Script
echo ===================================

echo.
echo Checking PHP installation...
php --version
if %errorlevel% neq 0 (
    echo ERROR: PHP is not installed or not in PATH
    echo Please install XAMPP first and add PHP to PATH
    pause
    exit /b 1
)

echo.
echo Checking Composer installation...
composer --version
if %errorlevel% neq 0 (
    echo ERROR: Composer is not installed or not in PATH
    echo Please install Composer first
    pause
    exit /b 1
)

echo.
echo Installing PHP dependencies...
composer install --no-dev --optimize-autoloader

echo.
echo Generating application key...
php artisan key:generate

echo.
echo Setting up storage permissions...
if not exist "storage\logs" mkdir "storage\logs"
if not exist "storage\framework\cache" mkdir "storage\framework\cache"
if not exist "storage\framework\sessions" mkdir "storage\framework\sessions"
if not exist "storage\framework\views" mkdir "storage\framework\views"
if not exist "bootstrap\cache" mkdir "bootstrap\cache"

echo.
echo ===================================
echo Setup completed!
echo ===================================
echo.
echo Next steps:
echo 1. Start XAMPP (Apache and MySQL)
echo 2. Create 'akaunting' database in phpMyAdmin
echo 3. Run: php artisan migrate --seed
echo 4. Open: http://localhost/akaunting
echo.
pause
