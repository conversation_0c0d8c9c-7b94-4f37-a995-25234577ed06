@echo off
setlocal enabledelayedexpansion
color 0A

echo.
echo ========================================================
echo                AKAUNTING SETUP SCRIPT
echo ========================================================
echo.

:: Set variables
set "XAMPP_PATH=C:\xampp"
set "HTDOCS_PATH=%XAMPP_PATH%\htdocs"
set "AKAUNTING_PATH=%HTDOCS_PATH%\akaunting"
set "CURRENT_DIR=%~dp0"

echo [INFO] Current directory: %CURRENT_DIR%
echo [INFO] Target XAMPP path: %XAMPP_PATH%
echo [INFO] Target Akaunting path: %AKAUNTING_PATH%
echo.

:: Check if running as administrator
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] This script requires administrator privileges!
    echo [ERROR] Please right-click and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

echo [SUCCESS] Running with administrator privileges
echo.

:: Step 1: Check XAMPP installation
echo ========================================================
echo STEP 1: Checking XAMPP Installation
echo ========================================================
if not exist "%XAMPP_PATH%" (
    echo [ERROR] XAMPP not found at %XAMPP_PATH%
    echo [INFO] Please install XAMPP first from: https://www.apachefriends.org/
    echo.
    pause
    exit /b 1
)
echo [SUCCESS] XAMPP directory found
echo.

:: Step 2: Check PHP installation
echo ========================================================
echo STEP 2: Checking PHP Installation
echo ========================================================
if exist "%XAMPP_PATH%\php\php.exe" (
    echo [SUCCESS] PHP found in XAMPP
    "%XAMPP_PATH%\php\php.exe" --version
    echo.
) else (
    echo [ERROR] PHP not found in XAMPP installation
    pause
    exit /b 1
)

:: Add PHP to PATH for current session
set "PATH=%XAMPP_PATH%\php;%PATH%"
echo [INFO] Added PHP to PATH for this session
echo.

:: Step 3: Check Composer installation
echo ========================================================
echo STEP 3: Checking Composer Installation
echo ========================================================
composer --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [WARNING] Composer not found in PATH
    echo [INFO] Attempting to install Composer...

    if exist "%CURRENT_DIR%composer-setup.exe" (
        echo [INFO] Found Composer installer, running it...
        start /wait "%CURRENT_DIR%composer-setup.exe" /VERYSILENT /SUPPRESSMSGBOXES /NORESTART
        echo [INFO] Composer installation completed
        echo [INFO] Please restart this script after Composer installation
        pause
        exit /b 0
    ) else (
        echo [ERROR] Composer installer not found
        echo [INFO] Please download and install Composer from: https://getcomposer.org/
        pause
        exit /b 1
    )
) else (
    echo [SUCCESS] Composer is installed
    composer --version
    echo.
)

:: Step 4: Create htdocs directory if it doesn't exist
echo ========================================================
echo STEP 4: Preparing Directories
echo ========================================================
if not exist "%HTDOCS_PATH%" (
    echo [INFO] Creating htdocs directory...
    mkdir "%HTDOCS_PATH%"
)
echo [SUCCESS] htdocs directory ready
echo.

:: Step 5: Copy Akaunting files to XAMPP htdocs
echo ========================================================
echo STEP 5: Copying Akaunting Files
echo ========================================================
if exist "%AKAUNTING_PATH%" (
    echo [WARNING] Akaunting directory already exists in htdocs
    echo [INFO] Skipping file copy...
) else (
    echo [INFO] Copying Akaunting files to XAMPP htdocs...
    xcopy "%CURRENT_DIR%*" "%AKAUNTING_PATH%\" /E /I /H /Y
    echo [SUCCESS] Files copied successfully
)
echo.

:: Step 6: Navigate to Akaunting directory
cd /d "%AKAUNTING_PATH%"
if %errorlevel% neq 0 (
    echo [ERROR] Could not navigate to Akaunting directory
    pause
    exit /b 1
)
echo [INFO] Working directory: %AKAUNTING_PATH%
echo.

:: Step 7: Install PHP dependencies
echo ========================================================
echo STEP 6: Installing PHP Dependencies
echo ========================================================
echo [INFO] Installing Composer dependencies (this may take a few minutes)...
composer install --no-dev --optimize-autoloader --no-interaction
if %errorlevel% neq 0 (
    echo [ERROR] Failed to install dependencies
    echo [INFO] Please check your internet connection and try again
    pause
    exit /b 1
)
echo [SUCCESS] Dependencies installed successfully
echo.

:: Step 8: Set up environment file
echo ========================================================
echo STEP 7: Setting Up Environment Configuration
echo ========================================================
if not exist ".env" (
    if exist ".env.example" (
        echo [INFO] Creating .env file from .env.example...
        copy ".env.example" ".env"
        echo [SUCCESS] .env file created
    ) else (
        echo [ERROR] .env.example file not found
        pause
        exit /b 1
    )
) else (
    echo [INFO] .env file already exists
)
echo.

:: Step 9: Generate application key
echo ========================================================
echo STEP 8: Generating Application Key
echo ========================================================
echo [INFO] Generating Laravel application key...
php artisan key:generate --no-interaction
if %errorlevel% neq 0 (
    echo [ERROR] Failed to generate application key
    pause
    exit /b 1
)
echo [SUCCESS] Application key generated
echo.

:: Step 10: Set up storage directories and permissions
echo ========================================================
echo STEP 9: Setting Up Storage Directories
echo ========================================================
echo [INFO] Creating storage directories...

if not exist "storage\logs" mkdir "storage\logs"
if not exist "storage\framework" mkdir "storage\framework"
if not exist "storage\framework\cache" mkdir "storage\framework\cache"
if not exist "storage\framework\sessions" mkdir "storage\framework\sessions"
if not exist "storage\framework\views" mkdir "storage\framework\views"
if not exist "storage\app" mkdir "storage\app"
if not exist "storage\app\public" mkdir "storage\app\public"
if not exist "bootstrap\cache" mkdir "bootstrap\cache"

echo [SUCCESS] Storage directories created
echo.

:: Step 11: Check XAMPP services
echo ========================================================
echo STEP 10: Checking XAMPP Services
echo ========================================================
echo [INFO] Checking if Apache is running...
netstat -an | find ":80 " | find "LISTENING" >nul
if %errorlevel% equ 0 (
    echo [SUCCESS] Apache appears to be running on port 80
) else (
    echo [WARNING] Apache is not running on port 80
    echo [INFO] Please start Apache from XAMPP Control Panel
)

echo [INFO] Checking if MySQL is running...
netstat -an | find ":3306 " | find "LISTENING" >nul
if %errorlevel% equ 0 (
    echo [SUCCESS] MySQL appears to be running on port 3306
) else (
    echo [WARNING] MySQL is not running on port 3306
    echo [INFO] Please start MySQL from XAMPP Control Panel
)
echo.

:: Step 12: Final instructions
echo ========================================================
echo                    SETUP COMPLETED!
echo ========================================================
echo.
echo [SUCCESS] Akaunting has been set up successfully!
echo.
echo NEXT STEPS:
echo 1. Start XAMPP Control Panel (if not already running)
echo 2. Start Apache and MySQL services
echo 3. Open phpMyAdmin: http://localhost/phpmyadmin
echo 4. Create a new database named 'akaunting'
echo 5. Run database migrations: php artisan migrate --seed
echo 6. Open Akaunting: http://localhost/akaunting
echo 7. Complete the web-based setup wizard
echo.
echo USEFUL COMMANDS:
echo - To run migrations: cd "%AKAUNTING_PATH%" ^&^& php artisan migrate --seed
echo - To start dev server: cd "%AKAUNTING_PATH%" ^&^& php artisan serve
echo - To clear cache: cd "%AKAUNTING_PATH%" ^&^& php artisan cache:clear
echo.
echo TROUBLESHOOTING:
echo - If you see permission errors, run this script as Administrator
echo - If database connection fails, check MySQL is running
echo - If Akaunting doesn't load, check Apache is running
echo.
echo Press any key to open XAMPP Control Panel...
pause >nul

:: Try to open XAMPP Control Panel
if exist "%XAMPP_PATH%\xampp-control.exe" (
    start "" "%XAMPP_PATH%\xampp-control.exe"
    echo [INFO] XAMPP Control Panel opened
) else (
    echo [WARNING] XAMPP Control Panel not found
    echo [INFO] Please manually start XAMPP services
)

echo.
echo Setup script completed. Check the instructions above.
pause
