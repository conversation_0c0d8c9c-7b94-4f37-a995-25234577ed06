# Akaunting Requirements Installation Script
# Run this script as Administrator

Write-Host "=== Akaunting Requirements Installation ===" -ForegroundColor Green

# Function to check if running as administrator
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

if (-not (Test-Administrator)) {
    Write-Host "ERROR: This script must be run as Administrator!" -ForegroundColor Red
    Write-Host "Please right-click PowerShell and select 'Run as Administrator'" -ForegroundColor Yellow
    exit 1
}

# Create directories
Write-Host "Creating directories..." -ForegroundColor Yellow
New-Item -ItemType Directory -Force -Path "C:\xampp"
New-Item -ItemType Directory -Force -Path "C:\xampp\htdocs"
New-Item -ItemType Directory -Force -Path "C:\php"

# Download and extract PHP
Write-Host "Downloading PHP 8.2..." -ForegroundColor Yellow
$phpUrl = "https://windows.php.net/downloads/releases/php-8.2.19-Win32-vs16-x64.zip"
$phpZip = "C:\php\php.zip"

try {
    Invoke-WebRequest -Uri $phpUrl -OutFile $phpZip
    Write-Host "Extracting PHP..." -ForegroundColor Yellow
    Expand-Archive -Path $phpZip -DestinationPath "C:\php" -Force
    Remove-Item $phpZip
    
    # Copy php.ini
    Copy-Item "C:\php\php.ini-production" "C:\php\php.ini"
    
    Write-Host "PHP installed successfully!" -ForegroundColor Green
} catch {
    Write-Host "Failed to download/install PHP: $($_.Exception.Message)" -ForegroundColor Red
}

# Add PHP to PATH
Write-Host "Adding PHP to system PATH..." -ForegroundColor Yellow
$currentPath = [Environment]::GetEnvironmentVariable("PATH", "Machine")
if ($currentPath -notlike "*C:\php*") {
    [Environment]::SetEnvironmentVariable("PATH", "$currentPath;C:\php", "Machine")
    Write-Host "PHP added to PATH!" -ForegroundColor Green
}

# Download MySQL (MariaDB)
Write-Host "Downloading MariaDB..." -ForegroundColor Yellow
$mariaUrl = "https://downloads.mariadb.org/rest-api/mariadb/10.11.8/mariadb-10.11.8-winx64.zip"
$mariaZip = "C:\xampp\mariadb.zip"

try {
    Invoke-WebRequest -Uri $mariaUrl -OutFile $mariaZip
    Write-Host "Extracting MariaDB..." -ForegroundColor Yellow
    Expand-Archive -Path $mariaZip -DestinationPath "C:\xampp" -Force
    Remove-Item $mariaZip
    
    # Rename the extracted folder
    $extractedFolder = Get-ChildItem "C:\xampp" | Where-Object { $_.Name -like "mariadb-*" } | Select-Object -First 1
    if ($extractedFolder) {
        Rename-Item $extractedFolder.FullName "C:\xampp\mysql"
    }
    
    Write-Host "MariaDB installed successfully!" -ForegroundColor Green
} catch {
    Write-Host "Failed to download/install MariaDB: $($_.Exception.Message)" -ForegroundColor Red
}

# Download Apache
Write-Host "Downloading Apache..." -ForegroundColor Yellow
$apacheUrl = "https://www.apachelounge.com/download/VS17/binaries/httpd-2.4.59-240404-win64-VS17.zip"
$apacheZip = "C:\xampp\apache.zip"

try {
    Invoke-WebRequest -Uri $apacheUrl -OutFile $apacheZip
    Write-Host "Extracting Apache..." -ForegroundColor Yellow
    Expand-Archive -Path $apacheZip -DestinationPath "C:\xampp\temp" -Force
    
    # Move Apache files
    $apacheFolder = Get-ChildItem "C:\xampp\temp" | Where-Object { $_.Name -like "Apache*" } | Select-Object -First 1
    if ($apacheFolder) {
        Move-Item "$($apacheFolder.FullName)\*" "C:\xampp\apache" -Force
    }
    
    Remove-Item "C:\xampp\temp" -Recurse -Force
    Remove-Item $apacheZip
    
    Write-Host "Apache installed successfully!" -ForegroundColor Green
} catch {
    Write-Host "Failed to download/install Apache: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "=== Installation Complete ===" -ForegroundColor Green
Write-Host "Please restart your PowerShell session to use the new PATH" -ForegroundColor Yellow
Write-Host "Then run: php --version" -ForegroundColor Yellow
