<!DOCTYPE html>
<html dir="<?php echo e(language()->direction()); ?>" lang="<?php echo e(app()->getLocale()); ?>">
    <?php if (isset($component)) { $__componentOriginal1f21be719e76a69248ef8f36ffe47304 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal1f21be719e76a69248ef8f36ffe47304 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layouts.install.head','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('layouts.install.head'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
         <?php $__env->slot('title', null, []); ?> 
            <?php echo !empty($title->attributes->has('title')) ? $title->attributes->get('title') : $title; ?>

         <?php $__env->endSlot(); ?>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal1f21be719e76a69248ef8f36ffe47304)): ?>
<?php $attributes = $__attributesOriginal1f21be719e76a69248ef8f36ffe47304; ?>
<?php unset($__attributesOriginal1f21be719e76a69248ef8f36ffe47304); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal1f21be719e76a69248ef8f36ffe47304)): ?>
<?php $component = $__componentOriginal1f21be719e76a69248ef8f36ffe47304; ?>
<?php unset($__componentOriginal1f21be719e76a69248ef8f36ffe47304); ?>
<?php endif; ?>

    <body>
        <?php echo $__env->yieldPushContent('body_start'); ?>

        <div class="h-screen lg:h-auto bg-no-repeat bg-cover bg-center" style="background-image: url(<?php echo e(asset('public/img/auth/login-bg.png')); ?>);">
            <?php if(! file_exists(public_path('js/install.min.js'))): ?>
                <div class="relative w-full lg:max-w-7xl flex flex-col lg:flex-row items-center m-auto">
                    <div class="md:w-6/12 h-screen hidden lg:flex flex-col items-center justify-center">
                        <img src="<?php echo e(asset('public/img/empty_pages/transactions.png')); ?>" alt="" />
                    </div>

                    <div class="w-full lg:w-46 h-31 flex flex-col justify-center gap-12 px-6 lg:px-24 py-24 mt-12 lg:mt-0">
                        <div class="flex flex-col gap-4">
                            <img src="<?php echo e(asset('public/img/akaunting-logo-green.svg')); ?>" class="w-16 my-3" alt="Akaunting" />

                            <div class="rounded-xl px-5 py-3 mb-5 bg-red-100 text-sm mb-0 text-red-600">
                                <?php echo trans('install.requirements.npm'); ?>

                            </div>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <div class="relative w-full lg:max-w-7xl flex items-center m-auto">
                    <?php if (isset($component)) { $__componentOriginalce0eb31d221aa144134c31f95700cdb3 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalce0eb31d221aa144134c31f95700cdb3 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layouts.auth.slider','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('layouts.auth.slider'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
                        <?php echo $slider ?? ''; ?>

                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalce0eb31d221aa144134c31f95700cdb3)): ?>
<?php $attributes = $__attributesOriginalce0eb31d221aa144134c31f95700cdb3; ?>
<?php unset($__attributesOriginalce0eb31d221aa144134c31f95700cdb3); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalce0eb31d221aa144134c31f95700cdb3)): ?>
<?php $component = $__componentOriginalce0eb31d221aa144134c31f95700cdb3; ?>
<?php unset($__componentOriginalce0eb31d221aa144134c31f95700cdb3); ?>
<?php endif; ?>

                    <div class="w-full lg:w-46 h-31 flex flex-col justify-center gap-12 px-6 lg:px-24 py-24 mt-12 lg:mt-0">
                        <div class="flex flex-col gap-4">
                            <img src="<?php echo e(asset('public/img/akaunting-logo-green.svg')); ?>" class="w-16 my-3" alt="Akaunting" />

                            <?php if (isset($component)) { $__componentOriginalfe2b15c7286c68bf33e1de9af40e2c80 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalfe2b15c7286c68bf33e1de9af40e2c80 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layouts.install.content','data' => ['title' => $title]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('layouts.install.content'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($title)]); ?>
                                <?php echo $content; ?>

                             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalfe2b15c7286c68bf33e1de9af40e2c80)): ?>
<?php $attributes = $__attributesOriginalfe2b15c7286c68bf33e1de9af40e2c80; ?>
<?php unset($__attributesOriginalfe2b15c7286c68bf33e1de9af40e2c80); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalfe2b15c7286c68bf33e1de9af40e2c80)): ?>
<?php $component = $__componentOriginalfe2b15c7286c68bf33e1de9af40e2c80; ?>
<?php unset($__componentOriginalfe2b15c7286c68bf33e1de9af40e2c80); ?>
<?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <?php echo $__env->yieldPushContent('body_end'); ?>

        <?php if (isset($component)) { $__componentOriginal19ebd2b25966fc0bcf2a2e7484a907de = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal19ebd2b25966fc0bcf2a2e7484a907de = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layouts.install.scripts','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('layouts.install.scripts'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal19ebd2b25966fc0bcf2a2e7484a907de)): ?>
<?php $attributes = $__attributesOriginal19ebd2b25966fc0bcf2a2e7484a907de; ?>
<?php unset($__attributesOriginal19ebd2b25966fc0bcf2a2e7484a907de); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal19ebd2b25966fc0bcf2a2e7484a907de)): ?>
<?php $component = $__componentOriginal19ebd2b25966fc0bcf2a2e7484a907de; ?>
<?php unset($__componentOriginal19ebd2b25966fc0bcf2a2e7484a907de); ?>
<?php endif; ?>
    </body>
</html>
<?php /**PATH C:\Users\<USER>\Desktop\akaunting\resources\views/components/layouts/install.blade.php ENDPATH**/ ?>