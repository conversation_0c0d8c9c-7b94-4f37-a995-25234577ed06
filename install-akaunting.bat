@echo off
setlocal enabledelayedexpansion
color 0F

echo.
echo ========================================================
echo          AKAUNTING COMPLETE INSTALLER
echo ========================================================
echo.
echo This script will install and set up Akaunting accounting
echo software on your Windows 11 PC with XAMPP stack.
echo.
echo What this script will do:
echo 1. Check system requirements
echo 2. Install XAMPP (Apache, MySQL, PHP 8.1+)
echo 3. Install Composer
echo 4. Set up Akaunting
echo 5. Configure environment
echo 6. Install dependencies
echo 7. Prepare database
echo.

:: Check if running as administrator
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] This script requires administrator privileges!
    echo [ERROR] Please right-click and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

echo [SUCCESS] Running with administrator privileges
echo.

echo Do you want to continue with the installation? (Y/N)
set /p "continue=Enter choice: "
if /i "!continue!" neq "Y" (
    echo [INFO] Installation cancelled by user
    pause
    exit /b 0
)

echo.
echo ========================================================
echo STEP 1: System Requirements Check
echo ========================================================

:: Run the test script first
if exist "test-setup.bat" (
    echo [INFO] Running system requirements check...
    call test-setup.bat
    echo.
    echo [INFO] Requirements check completed
    echo.
) else (
    echo [WARNING] Test script not found, skipping requirements check
)

echo ========================================================
echo STEP 2: XAMPP Installation
echo ========================================================

:: Check if XAMPP is already installed
if exist "C:\xampp\php\php.exe" (
    echo [INFO] XAMPP appears to be already installed
    C:\xampp\php\php.exe --version
    echo.
    echo [INFO] Do you want to skip XAMPP installation? (Y/N)
    set /p "skipXampp=Enter choice: "
    if /i "!skipXampp!" equ "Y" (
        echo [INFO] Skipping XAMPP installation
        goto :composer_check
    )
)

:: Install XAMPP
if exist "install-xampp.bat" (
    echo [INFO] Running XAMPP installer...
    call install-xampp.bat
    echo.
    echo [INFO] XAMPP installation completed
    echo.
) else (
    echo [ERROR] XAMPP installer script not found
    echo [INFO] Please install XAMPP manually from: https://www.apachefriends.org/
    pause
    exit /b 1
)

:composer_check
echo ========================================================
echo STEP 3: Composer Installation
echo ========================================================

:: Check if Composer is already installed
composer --version >nul 2>&1
if %errorlevel% equ 0 (
    echo [SUCCESS] Composer is already installed
    composer --version
    echo.
) else (
    echo [INFO] Installing Composer...
    if exist "composer-setup.exe" (
        echo [INFO] Running Composer installer...
        start /wait composer-setup.exe /VERYSILENT /SUPPRESSMSGBOXES /NORESTART
        echo [INFO] Composer installation completed
        echo [INFO] Please restart this script to continue
        pause
        exit /b 0
    ) else (
        echo [ERROR] Composer installer not found
        echo [INFO] Please download Composer from: https://getcomposer.org/
        pause
        exit /b 1
    )
)

echo ========================================================
echo STEP 4: Akaunting Setup
echo ========================================================

:: Run the main setup script
if exist "setup-akaunting.bat" (
    echo [INFO] Running Akaunting setup...
    call setup-akaunting.bat
    echo.
    echo [INFO] Akaunting setup completed
) else (
    echo [ERROR] Akaunting setup script not found
    pause
    exit /b 1
)

echo.
echo ========================================================
echo           INSTALLATION COMPLETED SUCCESSFULLY!
echo ========================================================
echo.
echo Akaunting has been installed and configured!
echo.
echo WHAT'S NEXT:
echo 1. Start XAMPP Control Panel
echo 2. Start Apache and MySQL services
echo 3. Open phpMyAdmin: http://localhost/phpmyadmin
echo 4. Create database 'akaunting' (if not done automatically)
echo 5. Run migrations: cd C:\xampp\htdocs\akaunting ^&^& php artisan migrate --seed
echo 6. Open Akaunting: http://localhost/akaunting
echo 7. Complete the web setup wizard
echo.
echo USEFUL LINKS:
echo - Akaunting: http://localhost/akaunting
echo - phpMyAdmin: http://localhost/phpmyadmin
echo - XAMPP Control Panel: C:\xampp\xampp-control.exe
echo.
echo TROUBLESHOOTING:
echo - If services don't start: Check Windows Firewall
echo - If database connection fails: Verify MySQL is running
echo - If Akaunting doesn't load: Check Apache is running
echo - For support: https://akaunting.com/support
echo.

:: Try to open XAMPP Control Panel
if exist "C:\xampp\xampp-control.exe" (
    echo [INFO] Opening XAMPP Control Panel...
    start "" "C:\xampp\xampp-control.exe"
)

echo.
echo Installation completed! Press any key to exit...
pause >nul
