[2025-06-14 15:39:31] production.ERROR: The "--compact" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--compact\" option does not exist. at C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\symfony\\console\\Input\\ArgvInput.php(152): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption('compact', NULL)
#1 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption('--compact')
#2 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('--compact', true)
#3 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 C:\\Users\\<USER>\\Desktop\\akaunting\\artisan(22): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-06-14 15:59:28] production.ERROR: SQLSTATE[HY000] [1049] Unknown database 'akaunting' (Connection: mysql, SQL: select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'akaunting' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1049): SQLSTATE[HY000] [1049] Unknown database 'akaunting' (Connection: mysql, SQL: select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'akaunting' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name) at C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select table_na...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select table_na...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(401): Illuminate\\Database\\Connection->select('select table_na...', Array, false)
#3 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\MySqlBuilder.php(41): Illuminate\\Database\\Connection->selectFromWriteConnection('select table_na...')
#4 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(165): Illuminate\\Database\\Schema\\MySqlBuilder->getTables(false)
#5 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(184): Illuminate\\Database\\Schema\\Builder->hasTable('migrations')
#6 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(727): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#7 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\StatusCommand.php(53): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#8 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\StatusCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#9 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\StatusCommand.php(52): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#10 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\StatusCommand->handle()
#11 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#12 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#14 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#15 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#16 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#17 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#18 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\StatusCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 C:\\Users\\<USER>\\Desktop\\akaunting\\artisan(22): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 {main}

[previous exception] [object] (PDOException(code: 1049): SQLSTATE[HY000] [1049] Unknown database 'akaunting' at C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:65)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(65): PDO->__construct('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#1 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'root', '', Array)
#2 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#6 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(528): Illuminate\\Database\\Connection->getPdo()
#7 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(false)
#8 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select table_na...', Array)
#9 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select table_na...', Array, Object(Closure))
#10 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select table_na...', Array, Object(Closure))
#11 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(401): Illuminate\\Database\\Connection->select('select table_na...', Array, false)
#12 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\MySqlBuilder.php(41): Illuminate\\Database\\Connection->selectFromWriteConnection('select table_na...')
#13 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(165): Illuminate\\Database\\Schema\\MySqlBuilder->getTables(false)
#14 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(184): Illuminate\\Database\\Schema\\Builder->hasTable('migrations')
#15 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(727): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#16 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\StatusCommand.php(53): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#17 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\StatusCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\StatusCommand.php(52): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\StatusCommand->handle()
#20 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#21 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#24 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#25 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\StatusCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\Users\\<USER>\\Desktop\\akaunting\\artisan(22): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 {main}
"} 
[2025-06-14 15:59:55] production.ERROR: Debugbar exception: SQLSTATE[HY000] [1049] Unknown database 'akaunting' (Connection: mysql, SQL: select count(*) as aggregate from `users` where (exists (select * from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `users`.`id` = `user_roles`.`user_id` and exists (select * from `permissions` inner join `role_permissions` on `permissions`.`id` = `role_permissions`.`permission_id` where `roles`.`id` = `role_permissions`.`role_id` and `name` = read-admin-panel)) or exists (select * from `permissions` inner join `user_permissions` on `permissions`.`id` = `user_permissions`.`permission_id` where `users`.`id` = `user_permissions`.`user_id` and `user_permissions`.`user_type` = users and `name` = read-admin-panel)) and `users`.`deleted_at` is null)  
[2025-06-14 15:59:55] production.DEBUG: Console command:: "php" "artisan" help  
[2025-06-14 15:59:55] production.DEBUG: Console output:: The command ""php" "artisan" help" failed.

Exit Code: 1(General error)

Working directory: C:\Users\<USER>\Desktop\akaunting

Output:
================


Error Output:
================
'"php"' is not recognized as an internal or external command,
operable program or batch file.
  
[2025-06-14 15:59:55] production.ERROR: Undefined constant "App\Utilities\AKAUNTING_PHP" {"exception":"[object] (Error(code: 0): Undefined constant \"App\\Utilities\\AKAUNTING_PHP\" at C:\\Users\\<USER>\\Desktop\\akaunting\\app\\Utilities\\Installer.php:124)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\akaunting\\app\\Http\\Controllers\\Install\\Requirements.php(19): App\\Utilities\\Installer::checkServerRequirements()
#1 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Install\\Requirements->show()
#2 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('show', Array)
#3 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Install\\Requirements), 'show')
#4 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#5 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#6 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#7 C:\\Users\\<USER>\\Desktop\\akaunting\\app\\Http\\Middleware\\CanInstall.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CanInstall->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\akaunting\\laravel-firewall\\src\\Abstracts\\Middleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Akaunting\\Firewall\\Abstracts\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\akaunting\\laravel-firewall\\src\\Abstracts\\Middleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Akaunting\\Firewall\\Abstracts\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\akaunting\\laravel-firewall\\src\\Abstracts\\Middleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Akaunting\\Firewall\\Abstracts\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\akaunting\\laravel-firewall\\src\\Abstracts\\Middleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Akaunting\\Firewall\\Abstracts\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\akaunting\\laravel-firewall\\src\\Abstracts\\Middleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Akaunting\\Firewall\\Abstracts\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\akaunting\\laravel-firewall\\src\\Abstracts\\Middleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Akaunting\\Firewall\\Abstracts\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\akaunting\\laravel-firewall\\src\\Abstracts\\Middleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Akaunting\\Firewall\\Abstracts\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\akaunting\\laravel-firewall\\src\\Abstracts\\Middleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Akaunting\\Firewall\\Abstracts\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\akaunting\\laravel-firewall\\src\\Abstracts\\Middleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Akaunting\\Firewall\\Abstracts\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\akaunting\\laravel-firewall\\src\\Abstracts\\Middleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Akaunting\\Firewall\\Abstracts\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\akaunting\\laravel-firewall\\src\\Abstracts\\Middleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Akaunting\\Firewall\\Abstracts\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\akaunting\\laravel-language\\src\\Middleware\\SetLocale.php(95): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Akaunting\\Language\\Middleware\\SetLocale->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\akaunting\\app\\Http\\Middleware\\AddXHeader.php(18): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\AddXHeader->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\akaunting\\app\\Http\\Middleware\\RedirectIfNotInstalled.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RedirectIfNotInstalled->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#52 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#54 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustHosts.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#76 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustHosts->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#77 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#78 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#79 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#80 C:\\Users\\<USER>\\Desktop\\akaunting\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#81 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\Users\M.Ahmad Shahbaz\Desktop\akaunting

Output:
================


Error Output:
================
'"php"' is not recognized as an internal or external command,
operable program or batch file.
  
[2025-06-14 16:02:23] production.ERROR: Undefined constant "App\Utilities\AKAUNTING_PHP" {"exception":"[object] (Error(code: 0): Undefined constant \"App\\Utilities\\AKAUNTING_PHP\" at C:\\Users\\<USER>\\Desktop\\akaunting\\app\\Utilities\\Installer.php:124)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\akaunting\\app\\Http\\Controllers\\Install\\Requirements.php(19): App\\Utilities\\Installer::checkServerRequirements()
#1 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Install\\Requirements->show()
#2 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('show', Array)
#3 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Install\\Requirements), 'show')
#4 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#5 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#6 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#7 C:\\Users\\<USER>\\Desktop\\akaunting\\app\\Http\\Middleware\\CanInstall.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CanInstall->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\akaunting\\laravel-firewall\\src\\Abstracts\\Middleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Akaunting\\Firewall\\Abstracts\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\akaunting\\laravel-firewall\\src\\Abstracts\\Middleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Akaunting\\Firewall\\Abstracts\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\akaunting\\laravel-firewall\\src\\Abstracts\\Middleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Akaunting\\Firewall\\Abstracts\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\akaunting\\laravel-firewall\\src\\Abstracts\\Middleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Akaunting\\Firewall\\Abstracts\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\akaunting\\laravel-firewall\\src\\Abstracts\\Middleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Akaunting\\Firewall\\Abstracts\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\akaunting\\laravel-firewall\\src\\Abstracts\\Middleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Akaunting\\Firewall\\Abstracts\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\akaunting\\laravel-firewall\\src\\Abstracts\\Middleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Akaunting\\Firewall\\Abstracts\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\akaunting\\laravel-firewall\\src\\Abstracts\\Middleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Akaunting\\Firewall\\Abstracts\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\akaunting\\laravel-firewall\\src\\Abstracts\\Middleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Akaunting\\Firewall\\Abstracts\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\akaunting\\laravel-firewall\\src\\Abstracts\\Middleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Akaunting\\Firewall\\Abstracts\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\akaunting\\laravel-firewall\\src\\Abstracts\\Middleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Akaunting\\Firewall\\Abstracts\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\akaunting\\laravel-language\\src\\Middleware\\SetLocale.php(95): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Akaunting\\Language\\Middleware\\SetLocale->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\akaunting\\app\\Http\\Middleware\\AddXHeader.php(18): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\AddXHeader->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\akaunting\\app\\Http\\Middleware\\RedirectIfNotInstalled.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RedirectIfNotInstalled->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#52 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#54 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustHosts.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#76 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustHosts->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#77 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#78 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#79 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#80 C:\\Users\\<USER>\\Desktop\\akaunting\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#81 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\Users\M.Ahmad Shahbaz\Desktop\akaunting

Output:
================


Error Output:
================
'"php"' is not recognized as an internal or external command,
operable program or batch file.
  
[2025-06-14 16:13:14] production.ERROR: Undefined constant "App\Utilities\AKAUNTING_PHP" {"exception":"[object] (Error(code: 0): Undefined constant \"App\\Utilities\\AKAUNTING_PHP\" at C:\\Users\\<USER>\\Desktop\\akaunting\\app\\Utilities\\Installer.php:124)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\akaunting\\app\\Http\\Controllers\\Install\\Requirements.php(19): App\\Utilities\\Installer::checkServerRequirements()
#1 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Install\\Requirements->show()
#2 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('show', Array)
#3 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Install\\Requirements), 'show')
#4 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#5 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#6 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#7 C:\\Users\\<USER>\\Desktop\\akaunting\\app\\Http\\Middleware\\CanInstall.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CanInstall->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\akaunting\\laravel-firewall\\src\\Abstracts\\Middleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Akaunting\\Firewall\\Abstracts\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\akaunting\\laravel-firewall\\src\\Abstracts\\Middleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Akaunting\\Firewall\\Abstracts\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\akaunting\\laravel-firewall\\src\\Abstracts\\Middleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Akaunting\\Firewall\\Abstracts\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\akaunting\\laravel-firewall\\src\\Abstracts\\Middleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Akaunting\\Firewall\\Abstracts\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\akaunting\\laravel-firewall\\src\\Abstracts\\Middleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Akaunting\\Firewall\\Abstracts\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\akaunting\\laravel-firewall\\src\\Abstracts\\Middleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Akaunting\\Firewall\\Abstracts\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\akaunting\\laravel-firewall\\src\\Abstracts\\Middleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Akaunting\\Firewall\\Abstracts\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\akaunting\\laravel-firewall\\src\\Abstracts\\Middleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Akaunting\\Firewall\\Abstracts\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\akaunting\\laravel-firewall\\src\\Abstracts\\Middleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Akaunting\\Firewall\\Abstracts\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\akaunting\\laravel-firewall\\src\\Abstracts\\Middleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Akaunting\\Firewall\\Abstracts\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\akaunting\\laravel-firewall\\src\\Abstracts\\Middleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Akaunting\\Firewall\\Abstracts\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\akaunting\\laravel-language\\src\\Middleware\\SetLocale.php(95): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Akaunting\\Language\\Middleware\\SetLocale->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\akaunting\\app\\Http\\Middleware\\AddXHeader.php(18): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\AddXHeader->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\akaunting\\app\\Http\\Middleware\\RedirectIfNotInstalled.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RedirectIfNotInstalled->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#52 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#54 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustHosts.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#76 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustHosts->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#77 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#78 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#79 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#80 C:\\Users\\<USER>\\Desktop\\akaunting\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#81 C:\\Users\\<USER>\\Desktop\\akaunting\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\Users\M.Ahmad Shahbaz\Desktop\akaunting

Output:
================


Error Output:
================
'"php"' is not recognized as an internal or external command,
operable program or batch file.
  
[2025-06-14 16:15:40] production.ERROR: Debugbar exception: SQLSTATE[HY000] [1049] Unknown database 'akaunting' (Connection: mysql, SQL: select count(*) as aggregate from `users` where (exists (select * from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `users`.`id` = `user_roles`.`user_id` and exists (select * from `permissions` inner join `role_permissions` on `permissions`.`id` = `role_permissions`.`permission_id` where `roles`.`id` = `role_permissions`.`role_id` and `name` = read-admin-panel)) or exists (select * from `permissions` inner join `user_permissions` on `permissions`.`id` = `user_permissions`.`permission_id` where `users`.`id` = `user_permissions`.`user_id` and `user_permissions`.`user_type` = users and `name` = read-admin-panel)) and `users`.`deleted_at` is null)  
[2025-06-14 16:15:41] production.ERROR: Debugbar exception: SQLSTATE[HY000] [1049] Unknown database 'akaunting' (Connection: mysql, SQL: select count(*) as aggregate from `users` where (exists (select * from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `users`.`id` = `user_roles`.`user_id` and exists (select * from `permissions` inner join `role_permissions` on `permissions`.`id` = `role_permissions`.`permission_id` where `roles`.`id` = `role_permissions`.`role_id` and `name` = read-admin-panel)) or exists (select * from `permissions` inner join `user_permissions` on `permissions`.`id` = `user_permissions`.`permission_id` where `users`.`id` = `user_permissions`.`user_id` and `user_permissions`.`user_type` = users and `name` = read-admin-panel)) and `users`.`deleted_at` is null)  
[2025-06-14 16:15:41] production.ERROR: Debugbar exception: SQLSTATE[HY000] [1049] Unknown database 'akaunting' (Connection: mysql, SQL: select count(*) as aggregate from `users` where (exists (select * from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `users`.`id` = `user_roles`.`user_id` and exists (select * from `permissions` inner join `role_permissions` on `permissions`.`id` = `role_permissions`.`permission_id` where `roles`.`id` = `role_permissions`.`role_id` and `name` = read-admin-panel)) or exists (select * from `permissions` inner join `user_permissions` on `permissions`.`id` = `user_permissions`.`permission_id` where `users`.`id` = `user_permissions`.`user_id` and `user_permissions`.`user_type` = users and `name` = read-admin-panel)) and `users`.`deleted_at` is null)  
[2025-06-14 16:15:42] production.ERROR: Debugbar exception: SQLSTATE[HY000] [1049] Unknown database 'akaunting' (Connection: mysql, SQL: select count(*) as aggregate from `users` where (exists (select * from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `users`.`id` = `user_roles`.`user_id` and exists (select * from `permissions` inner join `role_permissions` on `permissions`.`id` = `role_permissions`.`permission_id` where `roles`.`id` = `role_permissions`.`role_id` and `name` = read-admin-panel)) or exists (select * from `permissions` inner join `user_permissions` on `permissions`.`id` = `user_permissions`.`permission_id` where `users`.`id` = `user_permissions`.`user_id` and `user_permissions`.`user_type` = users and `name` = read-admin-panel)) and `users`.`deleted_at` is null)  
[2025-06-14 16:15:42] production.ERROR: Debugbar exception: SQLSTATE[HY000] [1049] Unknown database 'akaunting' (Connection: mysql, SQL: select count(*) as aggregate from `users` where (exists (select * from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `users`.`id` = `user_roles`.`user_id` and exists (select * from `permissions` inner join `role_permissions` on `permissions`.`id` = `role_permissions`.`permission_id` where `roles`.`id` = `role_permissions`.`role_id` and `name` = read-admin-panel)) or exists (select * from `permissions` inner join `user_permissions` on `permissions`.`id` = `user_permissions`.`permission_id` where `users`.`id` = `user_permissions`.`user_id` and `user_permissions`.`user_type` = users and `name` = read-admin-panel)) and `users`.`deleted_at` is null)  
[2025-06-14 16:15:42] production.ERROR: Debugbar exception: SQLSTATE[HY000] [1049] Unknown database 'akaunting' (Connection: mysql, SQL: select count(*) as aggregate from `users` where (exists (select * from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `users`.`id` = `user_roles`.`user_id` and exists (select * from `permissions` inner join `role_permissions` on `permissions`.`id` = `role_permissions`.`permission_id` where `roles`.`id` = `role_permissions`.`role_id` and `name` = read-admin-panel)) or exists (select * from `permissions` inner join `user_permissions` on `permissions`.`id` = `user_permissions`.`permission_id` where `users`.`id` = `user_permissions`.`user_id` and `user_permissions`.`user_type` = users and `name` = read-admin-panel)) and `users`.`deleted_at` is null)  
[2025-06-14 16:15:43] production.ERROR: Debugbar exception: SQLSTATE[HY000] [1049] Unknown database 'akaunting' (Connection: mysql, SQL: select count(*) as aggregate from `users` where (exists (select * from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `users`.`id` = `user_roles`.`user_id` and exists (select * from `permissions` inner join `role_permissions` on `permissions`.`id` = `role_permissions`.`permission_id` where `roles`.`id` = `role_permissions`.`role_id` and `name` = read-admin-panel)) or exists (select * from `permissions` inner join `user_permissions` on `permissions`.`id` = `user_permissions`.`permission_id` where `users`.`id` = `user_permissions`.`user_id` and `user_permissions`.`user_type` = users and `name` = read-admin-panel)) and `users`.`deleted_at` is null)  
[2025-06-14 16:15:43] production.ERROR: Debugbar exception: SQLSTATE[HY000] [1049] Unknown database 'akaunting' (Connection: mysql, SQL: select count(*) as aggregate from `users` where (exists (select * from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `users`.`id` = `user_roles`.`user_id` and exists (select * from `permissions` inner join `role_permissions` on `permissions`.`id` = `role_permissions`.`permission_id` where `roles`.`id` = `role_permissions`.`role_id` and `name` = read-admin-panel)) or exists (select * from `permissions` inner join `user_permissions` on `permissions`.`id` = `user_permissions`.`permission_id` where `users`.`id` = `user_permissions`.`user_id` and `user_permissions`.`user_type` = users and `name` = read-admin-panel)) and `users`.`deleted_at` is null)  
[2025-06-14 16:15:43] production.ERROR: Debugbar exception: SQLSTATE[HY000] [1049] Unknown database 'akaunting' (Connection: mysql, SQL: select count(*) as aggregate from `users` where (exists (select * from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `users`.`id` = `user_roles`.`user_id` and exists (select * from `permissions` inner join `role_permissions` on `permissions`.`id` = `role_permissions`.`permission_id` where `roles`.`id` = `role_permissions`.`role_id` and `name` = read-admin-panel)) or exists (select * from `permissions` inner join `user_permissions` on `permissions`.`id` = `user_permissions`.`permission_id` where `users`.`id` = `user_permissions`.`user_id` and `user_permissions`.`user_type` = users and `name` = read-admin-panel)) and `users`.`deleted_at` is null)  
[2025-06-14 16:15:44] production.ERROR: Debugbar exception: SQLSTATE[HY000] [1049] Unknown database 'akaunting' (Connection: mysql, SQL: select count(*) as aggregate from `users` where (exists (select * from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `users`.`id` = `user_roles`.`user_id` and exists (select * from `permissions` inner join `role_permissions` on `permissions`.`id` = `role_permissions`.`permission_id` where `roles`.`id` = `role_permissions`.`role_id` and `name` = read-admin-panel)) or exists (select * from `permissions` inner join `user_permissions` on `permissions`.`id` = `user_permissions`.`permission_id` where `users`.`id` = `user_permissions`.`user_id` and `user_permissions`.`user_type` = users and `name` = read-admin-panel)) and `users`.`deleted_at` is null)  
[2025-06-14 16:15:44] production.ERROR: Debugbar exception: SQLSTATE[HY000] [1049] Unknown database 'akaunting' (Connection: mysql, SQL: select count(*) as aggregate from `users` where (exists (select * from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `users`.`id` = `user_roles`.`user_id` and exists (select * from `permissions` inner join `role_permissions` on `permissions`.`id` = `role_permissions`.`permission_id` where `roles`.`id` = `role_permissions`.`role_id` and `name` = read-admin-panel)) or exists (select * from `permissions` inner join `user_permissions` on `permissions`.`id` = `user_permissions`.`permission_id` where `users`.`id` = `user_permissions`.`user_id` and `user_permissions`.`user_type` = users and `name` = read-admin-panel)) and `users`.`deleted_at` is null)  
[2025-06-14 16:15:45] production.ERROR: Debugbar exception: SQLSTATE[HY000] [1049] Unknown database 'akaunting' (Connection: mysql, SQL: select count(*) as aggregate from `users` where (exists (select * from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `users`.`id` = `user_roles`.`user_id` and exists (select * from `permissions` inner join `role_permissions` on `permissions`.`id` = `role_permissions`.`permission_id` where `roles`.`id` = `role_permissions`.`role_id` and `name` = read-admin-panel)) or exists (select * from `permissions` inner join `user_permissions` on `permissions`.`id` = `user_permissions`.`permission_id` where `users`.`id` = `user_permissions`.`user_id` and `user_permissions`.`user_type` = users and `name` = read-admin-panel)) and `users`.`deleted_at` is null)  
[2025-06-14 16:15:45] production.ERROR: Debugbar exception: SQLSTATE[HY000] [1049] Unknown database 'akaunting' (Connection: mysql, SQL: select count(*) as aggregate from `users` where (exists (select * from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `users`.`id` = `user_roles`.`user_id` and exists (select * from `permissions` inner join `role_permissions` on `permissions`.`id` = `role_permissions`.`permission_id` where `roles`.`id` = `role_permissions`.`role_id` and `name` = read-admin-panel)) or exists (select * from `permissions` inner join `user_permissions` on `permissions`.`id` = `user_permissions`.`permission_id` where `users`.`id` = `user_permissions`.`user_id` and `user_permissions`.`user_type` = users and `name` = read-admin-panel)) and `users`.`deleted_at` is null)  
[2025-06-14 16:15:45] production.DEBUG: Console command:: "php" "artisan" help  
[2025-06-14 16:15:46] production.DEBUG: Console output:: The command ""php" "artisan" help" failed.

Exit Code: 1(General error)

Working directory: C:\Users\<USER>\Desktop\akaunting

Output:
================


Error Output:
================
'"php"' is not recognized as an internal or external command,
operable program or batch file.
  
[2025-06-14 16:15:46] production.ERROR: Debugbar exception: SQLSTATE[HY000] [1049] Unknown database 'akaunting' (Connection: mysql, SQL: select count(*) as aggregate from `users` where (exists (select * from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `users`.`id` = `user_roles`.`user_id` and exists (select * from `permissions` inner join `role_permissions` on `permissions`.`id` = `role_permissions`.`permission_id` where `roles`.`id` = `role_permissions`.`role_id` and `name` = read-admin-panel)) or exists (select * from `permissions` inner join `user_permissions` on `permissions`.`id` = `user_permissions`.`permission_id` where `users`.`id` = `user_permissions`.`user_id` and `user_permissions`.`user_type` = users and `name` = read-admin-panel)) and `users`.`deleted_at` is null)  
[2025-06-14 16:15:46] production.DEBUG: Console command:: "php" "artisan" help  
[2025-06-14 16:15:46] production.DEBUG: Console output:: The command ""php" "artisan" help" failed.

Exit Code: 1(General error)

Working directory: C:\Users\<USER>\Desktop\akaunting

Output:
================


Error Output:
================
'"php"' is not recognized as an internal or external command,
operable program or batch file.
  
[2025-06-14 16:15:46] production.ERROR: Debugbar exception: SQLSTATE[HY000] [1049] Unknown database 'akaunting' (Connection: mysql, SQL: select count(*) as aggregate from `users` where (exists (select * from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `users`.`id` = `user_roles`.`user_id` and exists (select * from `permissions` inner join `role_permissions` on `permissions`.`id` = `role_permissions`.`permission_id` where `roles`.`id` = `role_permissions`.`role_id` and `name` = read-admin-panel)) or exists (select * from `permissions` inner join `user_permissions` on `permissions`.`id` = `user_permissions`.`permission_id` where `users`.`id` = `user_permissions`.`user_id` and `user_permissions`.`user_type` = users and `name` = read-admin-panel)) and `users`.`deleted_at` is null)  
[2025-06-14 16:15:46] production.ERROR: Debugbar exception: SQLSTATE[HY000] [1049] Unknown database 'akaunting' (Connection: mysql, SQL: select count(*) as aggregate from `users` where (exists (select * from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `users`.`id` = `user_roles`.`user_id` and exists (select * from `permissions` inner join `role_permissions` on `permissions`.`id` = `role_permissions`.`permission_id` where `roles`.`id` = `role_permissions`.`role_id` and `name` = read-admin-panel)) or exists (select * from `permissions` inner join `user_permissions` on `permissions`.`id` = `user_permissions`.`permission_id` where `users`.`id` = `user_permissions`.`user_id` and `user_permissions`.`user_type` = users and `name` = read-admin-panel)) and `users`.`deleted_at` is null)  
[2025-06-14 16:15:47] production.ERROR: Debugbar exception: SQLSTATE[HY000] [1049] Unknown database 'akaunting' (Connection: mysql, SQL: select count(*) as aggregate from `users` where (exists (select * from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `users`.`id` = `user_roles`.`user_id` and exists (select * from `permissions` inner join `role_permissions` on `permissions`.`id` = `role_permissions`.`permission_id` where `roles`.`id` = `role_permissions`.`role_id` and `name` = read-admin-panel)) or exists (select * from `permissions` inner join `user_permissions` on `permissions`.`id` = `user_permissions`.`permission_id` where `users`.`id` = `user_permissions`.`user_id` and `user_permissions`.`user_type` = users and `name` = read-admin-panel)) and `users`.`deleted_at` is null)  
[2025-06-14 16:15:47] production.ERROR: Debugbar exception: SQLSTATE[HY000] [1049] Unknown database 'akaunting' (Connection: mysql, SQL: select count(*) as aggregate from `users` where (exists (select * from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `users`.`id` = `user_roles`.`user_id` and exists (select * from `permissions` inner join `role_permissions` on `permissions`.`id` = `role_permissions`.`permission_id` where `roles`.`id` = `role_permissions`.`role_id` and `name` = read-admin-panel)) or exists (select * from `permissions` inner join `user_permissions` on `permissions`.`id` = `user_permissions`.`permission_id` where `users`.`id` = `user_permissions`.`user_id` and `user_permissions`.`user_type` = users and `name` = read-admin-panel)) and `users`.`deleted_at` is null)  
[2025-06-14 16:15:48] production.ERROR: Debugbar exception: SQLSTATE[HY000] [1049] Unknown database 'akaunting' (Connection: mysql, SQL: select count(*) as aggregate from `users` where (exists (select * from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `users`.`id` = `user_roles`.`user_id` and exists (select * from `permissions` inner join `role_permissions` on `permissions`.`id` = `role_permissions`.`permission_id` where `roles`.`id` = `role_permissions`.`role_id` and `name` = read-admin-panel)) or exists (select * from `permissions` inner join `user_permissions` on `permissions`.`id` = `user_permissions`.`permission_id` where `users`.`id` = `user_permissions`.`user_id` and `user_permissions`.`user_type` = users and `name` = read-admin-panel)) and `users`.`deleted_at` is null)  
[2025-06-14 16:15:48] production.ERROR: Debugbar exception: SQLSTATE[HY000] [1049] Unknown database 'akaunting' (Connection: mysql, SQL: select count(*) as aggregate from `users` where (exists (select * from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `users`.`id` = `user_roles`.`user_id` and exists (select * from `permissions` inner join `role_permissions` on `permissions`.`id` = `role_permissions`.`permission_id` where `roles`.`id` = `role_permissions`.`role_id` and `name` = read-admin-panel)) or exists (select * from `permissions` inner join `user_permissions` on `permissions`.`id` = `user_permissions`.`permission_id` where `users`.`id` = `user_permissions`.`user_id` and `user_permissions`.`user_type` = users and `name` = read-admin-panel)) and `users`.`deleted_at` is null)  
[2025-06-14 16:15:48] production.ERROR: Debugbar exception: SQLSTATE[HY000] [1049] Unknown database 'akaunting' (Connection: mysql, SQL: select count(*) as aggregate from `users` where (exists (select * from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `users`.`id` = `user_roles`.`user_id` and exists (select * from `permissions` inner join `role_permissions` on `permissions`.`id` = `role_permissions`.`permission_id` where `roles`.`id` = `role_permissions`.`role_id` and `name` = read-admin-panel)) or exists (select * from `permissions` inner join `user_permissions` on `permissions`.`id` = `user_permissions`.`permission_id` where `users`.`id` = `user_permissions`.`user_id` and `user_permissions`.`user_type` = users and `name` = read-admin-panel)) and `users`.`deleted_at` is null)  
[2025-06-14 16:15:49] production.ERROR: Debugbar exception: SQLSTATE[HY000] [1049] Unknown database 'akaunting' (Connection: mysql, SQL: select count(*) as aggregate from `users` where (exists (select * from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `users`.`id` = `user_roles`.`user_id` and exists (select * from `permissions` inner join `role_permissions` on `permissions`.`id` = `role_permissions`.`permission_id` where `roles`.`id` = `role_permissions`.`role_id` and `name` = read-admin-panel)) or exists (select * from `permissions` inner join `user_permissions` on `permissions`.`id` = `user_permissions`.`permission_id` where `users`.`id` = `user_permissions`.`user_id` and `user_permissions`.`user_type` = users and `name` = read-admin-panel)) and `users`.`deleted_at` is null)  
[2025-06-14 16:15:49] production.ERROR: Debugbar exception: SQLSTATE[HY000] [1049] Unknown database 'akaunting' (Connection: mysql, SQL: select count(*) as aggregate from `users` where (exists (select * from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `users`.`id` = `user_roles`.`user_id` and exists (select * from `permissions` inner join `role_permissions` on `permissions`.`id` = `role_permissions`.`permission_id` where `roles`.`id` = `role_permissions`.`role_id` and `name` = read-admin-panel)) or exists (select * from `permissions` inner join `user_permissions` on `permissions`.`id` = `user_permissions`.`permission_id` where `users`.`id` = `user_permissions`.`user_id` and `user_permissions`.`user_type` = users and `name` = read-admin-panel)) and `users`.`deleted_at` is null)  
[2025-06-14 16:15:49] production.ERROR: Debugbar exception: SQLSTATE[HY000] [1049] Unknown database 'akaunting' (Connection: mysql, SQL: select count(*) as aggregate from `users` where (exists (select * from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `users`.`id` = `user_roles`.`user_id` and exists (select * from `permissions` inner join `role_permissions` on `permissions`.`id` = `role_permissions`.`permission_id` where `roles`.`id` = `role_permissions`.`role_id` and `name` = read-admin-panel)) or exists (select * from `permissions` inner join `user_permissions` on `permissions`.`id` = `user_permissions`.`permission_id` where `users`.`id` = `user_permissions`.`user_id` and `user_permissions`.`user_type` = users and `name` = read-admin-panel)) and `users`.`deleted_at` is null)  
[2025-06-14 16:15:50] production.ERROR: Debugbar exception: SQLSTATE[HY000] [1049] Unknown database 'akaunting' (Connection: mysql, SQL: select count(*) as aggregate from `users` where (exists (select * from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `users`.`id` = `user_roles`.`user_id` and exists (select * from `permissions` inner join `role_permissions` on `permissions`.`id` = `role_permissions`.`permission_id` where `roles`.`id` = `role_permissions`.`role_id` and `name` = read-admin-panel)) or exists (select * from `permissions` inner join `user_permissions` on `permissions`.`id` = `user_permissions`.`permission_id` where `users`.`id` = `user_permissions`.`user_id` and `user_permissions`.`user_type` = users and `name` = read-admin-panel)) and `users`.`deleted_at` is null)  
[2025-06-14 16:15:50] production.ERROR: Debugbar exception: SQLSTATE[HY000] [1049] Unknown database 'akaunting' (Connection: mysql, SQL: select count(*) as aggregate from `users` where (exists (select * from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `users`.`id` = `user_roles`.`user_id` and exists (select * from `permissions` inner join `role_permissions` on `permissions`.`id` = `role_permissions`.`permission_id` where `roles`.`id` = `role_permissions`.`role_id` and `name` = read-admin-panel)) or exists (select * from `permissions` inner join `user_permissions` on `permissions`.`id` = `user_permissions`.`permission_id` where `users`.`id` = `user_permissions`.`user_id` and `user_permissions`.`user_type` = users and `name` = read-admin-panel)) and `users`.`deleted_at` is null)  
[2025-06-14 16:15:50] production.DEBUG: Console command:: "php" "artisan" help  
[2025-06-14 16:15:51] production.DEBUG: Console output:: The command ""php" "artisan" help" failed.

Exit Code: 1(General error)

Working directory: C:\Users\<USER>\Desktop\akaunting

Output:
================


Error Output:
================
'"php"' is not recognized as an internal or external command,
operable program or batch file.
  
[2025-06-14 16:15:51] production.ERROR: Debugbar exception: SQLSTATE[HY000] [1049] Unknown database 'akaunting' (Connection: mysql, SQL: select count(*) as aggregate from `users` where (exists (select * from `roles` inner join `user_roles` on `roles`.`id` = `user_roles`.`role_id` where `users`.`id` = `user_roles`.`user_id` and exists (select * from `permissions` inner join `role_permissions` on `permissions`.`id` = `role_permissions`.`permission_id` where `roles`.`id` = `role_permissions`.`role_id` and `name` = read-admin-panel)) or exists (select * from `permissions` inner join `user_permissions` on `permissions`.`id` = `user_permissions`.`permission_id` where `users`.`id` = `user_permissions`.`user_id` and `user_permissions`.`user_type` = users and `name` = read-admin-panel)) and `users`.`deleted_at` is null)  
