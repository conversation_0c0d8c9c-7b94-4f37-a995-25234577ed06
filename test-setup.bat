@echo off
setlocal enabledelayedexpansion
color 0E

echo.
echo ========================================================
echo           AKAUNTING SETUP SCRIPT TESTER
echo ========================================================
echo.

:: Test variables
set "TEST_PASSED=0"
set "TEST_FAILED=0"
set "XAMPP_PATH=C:\xampp"
set "CURRENT_DIR=%~dp0"

echo [TEST] Starting comprehensive setup script tests...
echo [TEST] Current directory: %CURRENT_DIR%
echo.

:: Test 1: Check if setup script exists
echo ========================================================
echo TEST 1: Setup Script Existence
echo ========================================================
if exist "%CURRENT_DIR%setup-akaunting.bat" (
    echo [PASS] setup-akaunting.bat found
    set /a TEST_PASSED+=1
) else (
    echo [FAIL] setup-akaunting.bat not found
    set /a TEST_FAILED+=1
)
echo.

:: Test 2: Check if .env file exists
echo ========================================================
echo TEST 2: Environment Configuration
echo ========================================================
if exist "%CURRENT_DIR%.env" (
    echo [PASS] .env file found
    set /a TEST_PASSED+=1
    
    :: Check if .env has required settings
    findstr /C:"DB_DATABASE=akaunting" "%CURRENT_DIR%.env" >nul
    if !errorlevel! equ 0 (
        echo [PASS] .env contains database configuration
        set /a TEST_PASSED+=1
    ) else (
        echo [FAIL] .env missing database configuration
        set /a TEST_FAILED+=1
    )
) else (
    echo [FAIL] .env file not found
    set /a TEST_FAILED+=1
)
echo.

:: Test 3: Check if .env.example exists
echo ========================================================
echo TEST 3: Environment Template
echo ========================================================
if exist "%CURRENT_DIR%.env.example" (
    echo [PASS] .env.example file found
    set /a TEST_PASSED+=1
) else (
    echo [FAIL] .env.example file not found
    set /a TEST_FAILED+=1
)
echo.

:: Test 4: Check if composer.json exists
echo ========================================================
echo TEST 4: Composer Configuration
echo ========================================================
if exist "%CURRENT_DIR%composer.json" (
    echo [PASS] composer.json found
    set /a TEST_PASSED+=1
    
    :: Check if it's a Laravel project
    findstr /C:"laravel/framework" "%CURRENT_DIR%composer.json" >nul
    if !errorlevel! equ 0 (
        echo [PASS] Laravel framework dependency found
        set /a TEST_PASSED+=1
    ) else (
        echo [FAIL] Laravel framework dependency not found
        set /a TEST_FAILED+=1
    )
) else (
    echo [FAIL] composer.json not found
    set /a TEST_FAILED+=1
)
echo.

:: Test 5: Check if artisan exists
echo ========================================================
echo TEST 5: Laravel Artisan
echo ========================================================
if exist "%CURRENT_DIR%artisan" (
    echo [PASS] artisan command file found
    set /a TEST_PASSED+=1
) else (
    echo [FAIL] artisan command file not found
    set /a TEST_FAILED+=1
)
echo.

:: Test 6: Check directory structure
echo ========================================================
echo TEST 6: Directory Structure
echo ========================================================
set "REQUIRED_DIRS=app config database public resources routes storage"
set "DIRS_FOUND=0"
set "DIRS_TOTAL=0"

for %%d in (%REQUIRED_DIRS%) do (
    set /a DIRS_TOTAL+=1
    if exist "%CURRENT_DIR%%%d" (
        echo [PASS] Directory found: %%d
        set /a DIRS_FOUND+=1
    ) else (
        echo [FAIL] Directory missing: %%d
    )
)

if !DIRS_FOUND! equ !DIRS_TOTAL! (
    echo [PASS] All required directories found
    set /a TEST_PASSED+=1
) else (
    echo [FAIL] Missing !DIRS_TOTAL! - !DIRS_FOUND! directories
    set /a TEST_FAILED+=1
)
echo.

:: Test 7: Check if Composer installer exists
echo ========================================================
echo TEST 7: Composer Installer
echo ========================================================
if exist "%CURRENT_DIR%composer-setup.exe" (
    echo [PASS] Composer installer found
    set /a TEST_PASSED+=1
) else (
    echo [WARN] Composer installer not found (may already be installed)
    echo [INFO] This is not critical if Composer is already installed
)
echo.

:: Test 8: Check system requirements
echo ========================================================
echo TEST 8: System Requirements
echo ========================================================

:: Check Windows version
ver | findstr /C:"Windows" >nul
if !errorlevel! equ 0 (
    echo [PASS] Running on Windows
    set /a TEST_PASSED+=1
) else (
    echo [FAIL] Not running on Windows
    set /a TEST_FAILED+=1
)

:: Check if running as admin
net session >nul 2>&1
if !errorlevel! equ 0 (
    echo [PASS] Running with administrator privileges
    set /a TEST_PASSED+=1
) else (
    echo [WARN] Not running as administrator
    echo [INFO] Setup script will require admin privileges
)
echo.

:: Test 9: Simulate setup script execution (dry run)
echo ========================================================
echo TEST 9: Setup Script Validation
echo ========================================================
echo [INFO] Performing syntax check on setup script...

:: Check for common batch file syntax issues
findstr /C:"@echo off" "%CURRENT_DIR%setup-akaunting.bat" >nul
if !errorlevel! equ 0 (
    echo [PASS] Setup script has proper header
    set /a TEST_PASSED+=1
) else (
    echo [FAIL] Setup script missing proper header
    set /a TEST_FAILED+=1
)

findstr /C:"setlocal" "%CURRENT_DIR%setup-akaunting.bat" >nul
if !errorlevel! equ 0 (
    echo [PASS] Setup script uses local variables
    set /a TEST_PASSED+=1
) else (
    echo [WARN] Setup script may not use local variables
)

findstr /C:"pause" "%CURRENT_DIR%setup-akaunting.bat" >nul
if !errorlevel! equ 0 (
    echo [PASS] Setup script has user interaction
    set /a TEST_PASSED+=1
) else (
    echo [FAIL] Setup script missing user interaction
    set /a TEST_FAILED+=1
)
echo.

:: Test 10: Check for potential XAMPP installation
echo ========================================================
echo TEST 10: XAMPP Detection
echo ========================================================
if exist "%XAMPP_PATH%" (
    echo [PASS] XAMPP directory found at %XAMPP_PATH%
    set /a TEST_PASSED+=1
    
    if exist "%XAMPP_PATH%\php\php.exe" (
        echo [PASS] PHP executable found in XAMPP
        set /a TEST_PASSED+=1
    ) else (
        echo [FAIL] PHP executable not found in XAMPP
        set /a TEST_FAILED+=1
    )
    
    if exist "%XAMPP_PATH%\mysql\bin\mysql.exe" (
        echo [PASS] MySQL executable found in XAMPP
        set /a TEST_PASSED+=1
    ) else (
        echo [FAIL] MySQL executable not found in XAMPP
        set /a TEST_FAILED+=1
    )
) else (
    echo [INFO] XAMPP not found at %XAMPP_PATH%
    echo [INFO] Setup script will guide user to install XAMPP
)
echo.

:: Display test results
echo ========================================================
echo                    TEST RESULTS
echo ========================================================
echo.
echo Tests Passed: !TEST_PASSED!
echo Tests Failed: !TEST_FAILED!
echo.

if !TEST_FAILED! equ 0 (
    echo [SUCCESS] All critical tests passed!
    echo [SUCCESS] Setup script should work correctly
    color 0A
) else (
    echo [WARNING] Some tests failed
    echo [WARNING] Setup script may encounter issues
    color 0C
)

echo.
echo RECOMMENDATIONS:
if !TEST_FAILED! gtr 0 (
    echo - Fix the failed tests before running setup
)
echo - Run setup-akaunting.bat as Administrator
echo - Ensure internet connection for downloading dependencies
echo - Have XAMPP installer ready if not already installed
echo.

echo Press any key to continue...
pause >nul
