<?php return array (
  'akaunting/laravel-apexcharts' => 
  array (
    'aliases' => 
    array (
      'Apexcharts' => 'Akaunting\\Apexcharts\\Facade',
    ),
    'providers' => 
    array (
      0 => 'Akaunting\\Apexcharts\\Provider',
    ),
  ),
  'akaunting/laravel-debugbar-collector' => 
  array (
    'providers' => 
    array (
      0 => 'Akaunting\\DebugbarCollector\\Provider',
    ),
  ),
  'akaunting/laravel-firewall' => 
  array (
    'providers' => 
    array (
      0 => 'Akaunting\\Firewall\\Provider',
    ),
  ),
  'akaunting/laravel-language' => 
  array (
    'aliases' => 
    array (
      'Language' => 'Akaunting\\Language\\Facade',
    ),
    'providers' => 
    array (
      0 => 'Akaunting\\Language\\Provider',
    ),
  ),
  'akaunting/laravel-menu' => 
  array (
    'aliases' => 
    array (
      'Menu' => 'Akaunting\\Menu\\Facade',
    ),
    'providers' => 
    array (
      0 => 'Akaunting\\Menu\\Provider',
    ),
  ),
  'akaunting/laravel-module' => 
  array (
    'aliases' => 
    array (
      'Module' => 'Akaunting\\Module\\Facade',
    ),
    'providers' => 
    array (
      0 => 'Akaunting\\Module\\Providers\\Laravel',
    ),
  ),
  'akaunting/laravel-money' => 
  array (
    'providers' => 
    array (
      0 => 'Akaunting\\Money\\Provider',
    ),
  ),
  'akaunting/laravel-mutable-observer' => 
  array (
    'providers' => 
    array (
      0 => 'Akaunting\\MutableObserver\\Provider',
    ),
  ),
  'akaunting/laravel-setting' => 
  array (
    'aliases' => 
    array (
      'Setting' => 'Akaunting\\Setting\\Facade',
    ),
    'providers' => 
    array (
      0 => 'Akaunting\\Setting\\Provider',
    ),
  ),
  'akaunting/laravel-sortable' => 
  array (
    'providers' => 
    array (
      0 => 'Akaunting\\Sortable\\Provider',
    ),
  ),
  'akaunting/laravel-version' => 
  array (
    'aliases' => 
    array (
      'Version' => 'Akaunting\\Version\\Facade',
    ),
    'providers' => 
    array (
      0 => 'Akaunting\\Version\\Provider',
    ),
  ),
  'barryvdh/laravel-debugbar' => 
  array (
    'aliases' => 
    array (
      'Debugbar' => 'Barryvdh\\Debugbar\\Facades\\Debugbar',
    ),
    'providers' => 
    array (
      0 => 'Barryvdh\\Debugbar\\ServiceProvider',
    ),
  ),
  'barryvdh/laravel-dompdf' => 
  array (
    'aliases' => 
    array (
      'PDF' => 'Barryvdh\\DomPDF\\Facade\\Pdf',
      'Pdf' => 'Barryvdh\\DomPDF\\Facade\\Pdf',
    ),
    'providers' => 
    array (
      0 => 'Barryvdh\\DomPDF\\ServiceProvider',
    ),
  ),
  'barryvdh/laravel-ide-helper' => 
  array (
    'providers' => 
    array (
      0 => 'Barryvdh\\LaravelIdeHelper\\IdeHelperServiceProvider',
    ),
  ),
  'bkwld/cloner' => 
  array (
    'providers' => 
    array (
      0 => 'Bkwld\\Cloner\\ServiceProvider',
    ),
  ),
  'genealabs/laravel-model-caching' => 
  array (
    'providers' => 
    array (
      0 => 'GeneaLabs\\LaravelModelCaching\\Providers\\Service',
    ),
  ),
  'graham-campbell/markdown' => 
  array (
    'providers' => 
    array (
      0 => 'GrahamCampbell\\Markdown\\MarkdownServiceProvider',
    ),
  ),
  'intervention/image' => 
  array (
    'aliases' => 
    array (
      'Image' => 'Intervention\\Image\\Facades\\Image',
    ),
    'providers' => 
    array (
      0 => 'Intervention\\Image\\ImageServiceProvider',
    ),
  ),
  'jenssegers/agent' => 
  array (
    'aliases' => 
    array (
      'Agent' => 'Jenssegers\\Agent\\Facades\\Agent',
    ),
    'providers' => 
    array (
      0 => 'Jenssegers\\Agent\\AgentServiceProvider',
    ),
  ),
  'laracasts/flash' => 
  array (
    'aliases' => 
    array (
      'Flash' => 'Laracasts\\Flash\\Flash',
    ),
    'providers' => 
    array (
      0 => 'Laracasts\\Flash\\FlashServiceProvider',
    ),
  ),
  'laravel/sanctum' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Sanctum\\SanctumServiceProvider',
    ),
  ),
  'laravel/slack-notification-channel' => 
  array (
    'providers' => 
    array (
      0 => 'Illuminate\\Notifications\\SlackChannelServiceProvider',
    ),
  ),
  'laravel/tinker' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Tinker\\TinkerServiceProvider',
    ),
  ),
  'laravel/ui' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Ui\\UiServiceProvider',
    ),
  ),
  'laravelcollective/html' => 
  array (
    'providers' => 
    array (
      0 => 'Collective\\Html\\HtmlServiceProvider',
    ),
    'aliases' => 
    array (
      'Form' => 'Collective\\Html\\FormFacade',
      'Html' => 'Collective\\Html\\HtmlFacade',
    ),
  ),
  'livewire/livewire' => 
  array (
    'aliases' => 
    array (
      'Livewire' => 'Livewire\\Livewire',
    ),
    'providers' => 
    array (
      0 => 'Livewire\\LivewireServiceProvider',
    ),
  ),
  'lorisleiva/laravel-search-string' => 
  array (
    'providers' => 
    array (
      0 => 'Lorisleiva\\LaravelSearchString\\ServiceProvider',
    ),
  ),
  'maatwebsite/excel' => 
  array (
    'aliases' => 
    array (
      'Excel' => 'Maatwebsite\\Excel\\Facades\\Excel',
    ),
    'providers' => 
    array (
      0 => 'Maatwebsite\\Excel\\ExcelServiceProvider',
    ),
  ),
  'nesbot/carbon' => 
  array (
    'providers' => 
    array (
      0 => 'Carbon\\Laravel\\ServiceProvider',
    ),
  ),
  'nunomaduro/termwind' => 
  array (
    'providers' => 
    array (
      0 => 'Termwind\\Laravel\\TermwindServiceProvider',
    ),
  ),
  'plank/laravel-mediable' => 
  array (
    'aliases' => 
    array (
      'MediaUploader' => 'Plank\\Mediable\\Facades\\MediaUploader',
      'ImageManipulator' => 'Plank\\Mediable\\Facades\\ImageManipulator',
    ),
    'providers' => 
    array (
      0 => 'Plank\\Mediable\\MediableServiceProvider',
    ),
  ),
  'riverskies/laravel-mobile-detect' => 
  array (
    'aliases' => 
    array (
      'MobileDetect' => 'Riverskies\\Laravel\\MobileDetect\\Facades\\MobileDetect',
    ),
    'providers' => 
    array (
      0 => 'Riverskies\\Laravel\\MobileDetect\\MobileDetectServiceProvider',
    ),
  ),
  'santigarcor/laratrust' => 
  array (
    'aliases' => 
    array (
      'Laratrust' => 'Laratrust\\LaratrustFacade',
    ),
    'providers' => 
    array (
      0 => 'Laratrust\\LaratrustServiceProvider',
    ),
  ),
  'sentry/sentry-laravel' => 
  array (
    'aliases' => 
    array (
      'Sentry' => 'Sentry\\Laravel\\Facade',
    ),
    'providers' => 
    array (
      0 => 'Sentry\\Laravel\\ServiceProvider',
      1 => 'Sentry\\Laravel\\Tracing\\ServiceProvider',
    ),
  ),
  'staudenmeir/belongs-to-through' => 
  array (
    'providers' => 
    array (
      0 => 'Staudenmeir\\BelongsToThrough\\IdeHelperServiceProvider',
    ),
  ),
  'staudenmeir/eloquent-has-many-deep' => 
  array (
    'providers' => 
    array (
      0 => 'Staudenmeir\\EloquentHasManyDeep\\IdeHelperServiceProvider',
    ),
  ),
);