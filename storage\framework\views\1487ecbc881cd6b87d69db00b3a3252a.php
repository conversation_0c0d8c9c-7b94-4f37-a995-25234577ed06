<?php if (isset($component)) { $__componentOriginal74e861309dfc9b9ab6d1aa2ec05b6057 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal74e861309dfc9b9ab6d1aa2ec05b6057 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layouts.error','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('layouts.error'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('title', null, []); ?> 
        <?php echo e(trans('errors.title.404')); ?>

     <?php $__env->endSlot(); ?>

     <?php $__env->slot('content', null, []); ?> 
        <div class="h-full flex flex-col sm:flex-row items-center justify-center sm:justify-between xl:-ml-64">
            <div class="flex flex-col items-start gap-y-4 mb-10 sm:mb-0 sm:-mt-24">
                <h1 class="font-medium text-5xl lg:text-8xl">
                    <?php echo e(trans('errors.header.404')); ?>

                </h1>

                <span class="text-lg">
                    <?php echo e(trans('errors.title.404')); ?>

                </span>

                <?php if(! empty($message)): ?>
                <span class="text-lg">
                    <?php echo e($message); ?>

                </span>
                <?php endif; ?>

                <?php $landing_page = user() ? user()->getLandingPageOfUser() : route('login'); ?>
                <?php if (isset($component)) { $__componentOriginal6768a8abc6dec1bd6c8b1a9d996e6c97 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6768a8abc6dec1bd6c8b1a9d996e6c97 = $attributes; } ?>
<?php $component = App\View\Components\Link::resolve(['class' => 'relative flex items-center justify-center bg-green hover:bg-green-700 text-white px-6 py-1.5 text-base rounded-lg disabled:bg-green-100 mt-3','override' => 'class'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('link'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\Link::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['href' => ''.e($landing_page).'']); ?>
                    <?php echo e(trans('general.go_to_dashboard')); ?>

                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6768a8abc6dec1bd6c8b1a9d996e6c97)): ?>
<?php $attributes = $__attributesOriginal6768a8abc6dec1bd6c8b1a9d996e6c97; ?>
<?php unset($__attributesOriginal6768a8abc6dec1bd6c8b1a9d996e6c97); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6768a8abc6dec1bd6c8b1a9d996e6c97)): ?>
<?php $component = $__componentOriginal6768a8abc6dec1bd6c8b1a9d996e6c97; ?>
<?php unset($__componentOriginal6768a8abc6dec1bd6c8b1a9d996e6c97); ?>
<?php endif; ?>
            </div>

            <img src="<?php echo e(asset('public/img/errors/404.png')); ?>" alt="404" />
        </div>
     <?php $__env->endSlot(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal74e861309dfc9b9ab6d1aa2ec05b6057)): ?>
<?php $attributes = $__attributesOriginal74e861309dfc9b9ab6d1aa2ec05b6057; ?>
<?php unset($__attributesOriginal74e861309dfc9b9ab6d1aa2ec05b6057); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal74e861309dfc9b9ab6d1aa2ec05b6057)): ?>
<?php $component = $__componentOriginal74e861309dfc9b9ab6d1aa2ec05b6057; ?>
<?php unset($__componentOriginal74e861309dfc9b9ab6d1aa2ec05b6057); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Desktop\akaunting\resources\views/errors/404.blade.php ENDPATH**/ ?>