<!DOCTYPE html>
<html dir="<?php echo e(language()->direction()); ?>" lang="<?php echo e(app()->getLocale()); ?>">
    <?php if (isset($component)) { $__componentOriginalebc0871e56479783604a1621e24dbe3b = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalebc0871e56479783604a1621e24dbe3b = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layouts.error.head','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('layouts.error.head'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
         <?php $__env->slot('title', null, []); ?> 
            <?php echo !empty($title->attributes->has('title')) ? $title->attributes->get('title') : $title; ?>

         <?php $__env->endSlot(); ?>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalebc0871e56479783604a1621e24dbe3b)): ?>
<?php $attributes = $__attributesOriginalebc0871e56479783604a1621e24dbe3b; ?>
<?php unset($__attributesOriginalebc0871e56479783604a1621e24dbe3b); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalebc0871e56479783604a1621e24dbe3b)): ?>
<?php $component = $__componentOriginalebc0871e56479783604a1621e24dbe3b; ?>
<?php unset($__componentOriginalebc0871e56479783604a1621e24dbe3b); ?>
<?php endif; ?>

    <?php if (app('mobile-detect')->isMobile() && !app('mobile-detect')->isTablet()) : ?>
    <body class="bg-body">
    <?php else: ?>
    <body class="bg-body overflow-y-overlay">
    <?php endif; ?>

        <?php echo $__env->yieldPushContent('body_start'); ?>

        <div class="main-content xl:ltr:ml-64  xl:rtl:mr-64 transition-all ease-in-out" id="panel">
            <div id="main-body">
                <div class="container">
                    <?php if (isset($component)) { $__componentOriginalc5745c6db92e5ee8a69d6bf4eb689df1 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc5745c6db92e5ee8a69d6bf4eb689df1 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layouts.error.content','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('layouts.error.content'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
                        <?php echo $content; ?>

                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc5745c6db92e5ee8a69d6bf4eb689df1)): ?>
<?php $attributes = $__attributesOriginalc5745c6db92e5ee8a69d6bf4eb689df1; ?>
<?php unset($__attributesOriginalc5745c6db92e5ee8a69d6bf4eb689df1); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc5745c6db92e5ee8a69d6bf4eb689df1)): ?>
<?php $component = $__componentOriginalc5745c6db92e5ee8a69d6bf4eb689df1; ?>
<?php unset($__componentOriginalc5745c6db92e5ee8a69d6bf4eb689df1); ?>
<?php endif; ?>

                </div>
            </div>
        </div>

        <?php echo $__env->yieldPushContent('body_end'); ?>

        <?php if (isset($component)) { $__componentOriginal414d7ad6fb80f7ac99228c0176813967 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal414d7ad6fb80f7ac99228c0176813967 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layouts.error.scripts','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('layouts.error.scripts'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal414d7ad6fb80f7ac99228c0176813967)): ?>
<?php $attributes = $__attributesOriginal414d7ad6fb80f7ac99228c0176813967; ?>
<?php unset($__attributesOriginal414d7ad6fb80f7ac99228c0176813967); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal414d7ad6fb80f7ac99228c0176813967)): ?>
<?php $component = $__componentOriginal414d7ad6fb80f7ac99228c0176813967; ?>
<?php unset($__componentOriginal414d7ad6fb80f7ac99228c0176813967); ?>
<?php endif; ?>
    </body>
</html>
<?php /**PATH C:\Users\<USER>\Desktop\akaunting\resources\views/components/layouts/error.blade.php ENDPATH**/ ?>