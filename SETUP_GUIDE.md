# Akaunting Setup Guide for Windows 11

## Current Status
✅ Akaunting source code is ready  
✅ Environment configuration (.env) is created  
✅ Composer installer downloaded  
❌ XAMPP/PHP not yet installed  
❌ Dependencies not installed  

## Manual Installation Steps

### Step 1: Install XAMPP
Since automated installation had issues, please:

1. **Download XAMPP manually:**
   - Go to: https://www.apachefriends.org/download.html
   - Download XAMPP for Windows (PHP 8.1 or higher)
   - Run the installer as Administrator
   - Install to `C:\xampp`

2. **Verify XAMPP installation:**
   ```powershell
   # After installation, test PHP
   C:\xampp\php\php.exe --version
   ```

3. **Add PHP to PATH:**
   - Add `C:\xampp\php` to your system PATH environment variable
   - Restart PowerShell/Command Prompt

### Step 2: Install Composer
We already downloaded the Composer installer. Run it manually:

1. **Run the installer:**
   - Navigate to your akaunting folder
   - Double-click `composer-setup.exe`
   - Follow the installation wizard
   - It should detect PHP automatically

2. **Verify Composer:**
   ```powershell
   composer --version
   ```

### Step 3: Move Akaunting to XAMPP
```powershell
# Copy the entire akaunting folder to XAMPP htdocs
Copy-Item -Path "C:\Users\<USER>\Desktop\akaunting" -Destination "C:\xampp\htdocs\akaunting" -Recurse
```

### Step 4: Install Dependencies
```powershell
cd C:\xampp\htdocs\akaunting
composer install --no-dev
```

### Step 5: Database Setup
1. **Start XAMPP services:**
   - Open XAMPP Control Panel
   - Start Apache and MySQL

2. **Create database:**
   - Open http://localhost/phpmyadmin
   - Create new database named `akaunting`

### Step 6: Laravel Setup
```powershell
cd C:\xampp\htdocs\akaunting
php artisan key:generate
php artisan migrate --seed
```

### Step 7: Launch Application
- Open browser to: http://localhost/akaunting
- Complete the web setup wizard

## Alternative: Portable Installation

If XAMPP installation continues to have issues, you can use a portable approach:

1. **Download portable PHP:**
   - Download PHP 8.2 ZIP from https://windows.php.net/download/
   - Extract to `C:\php`

2. **Download portable MySQL:**
   - Download MySQL ZIP distribution
   - Extract and configure

3. **Use built-in PHP server for testing:**
   ```powershell
   cd C:\Users\<USER>\Desktop\akaunting
   php -S localhost:8000 -t public
   ```

## Next Steps After Installation

Once you have PHP and Composer working, run:
```powershell
cd C:\Users\<USER>\Desktop\akaunting
composer install --no-dev
php artisan key:generate
```

## Troubleshooting

- If PHP is not found: Check PATH environment variable
- If Composer fails: Ensure PHP is working first
- If database connection fails: Check MySQL is running
- If permissions issues: Run as Administrator
