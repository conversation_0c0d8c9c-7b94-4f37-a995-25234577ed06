// Minimal install.min.js to handle Akaunting installation process
// This handles form submissions and redirects for the installation wizard

console.log('Akaunting Installation JavaScript loaded');

// Basic AJAX form submission handler
function submitForm(form, callback) {
    const formData = new FormData(form);

    fetch(form.action, {
        method: form.method || 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': window.Laravel ? window.Laravel.csrfToken : ''
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.redirect) {
            // Handle redirect like the original Form plugin
            window.location.href = data.redirect;
        } else if (callback) {
            callback(data);
        }
    })
    .catch(error => {
        console.error('Form submission error:', error);
        if (callback) {
            callback({error: error});
        }
    });
}

// Basic functionality for installation process
document.addEventListener('DOMContentLoaded', function() {
    console.log('Installation page loaded');

    // Handle form submissions with AJAX
    const forms = document.querySelectorAll('form');
    forms.forEach(function(form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            console.log('Form submitted via AJAX');

            // Show loading state if button exists
            const submitButton = form.querySelector('button[type="submit"], input[type="submit"]');
            if (submitButton) {
                submitButton.disabled = true;
                const originalText = submitButton.textContent;
                submitButton.textContent = 'Loading...';

                submitForm(form, function(data) {
                    submitButton.disabled = false;
                    submitButton.textContent = originalText;
                });
            } else {
                submitForm(form);
            }
        });
    });

    // Handle language selection specifically
    const langSelect = document.querySelector('select[name="lang"]');
    if (langSelect) {
        console.log('Language selector found');
    }
});

// Export for module compatibility
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {};
}
