@font-face {
  font-family: 'Material Icons';
  font-style: normal;
  font-weight: 100;
  src: url(./Regular/MaterialIcons-Regular.eot); /* For IE6-8 */
  src: local('Material Icons'),
    local('MaterialIcons-Regular'),
    url(./Regular/MaterialIcons-Regular.woff2) format('woff2'),
    url(./Regular/MaterialIcons-Regular.woff) format('woff'),
    url(./Regular/MaterialIcons-Regular.ttf) format('truetype');
}

@font-face {
  font-family: 'Material Icons Outlined';
  font-style: normal;
  font-weight: 100;
  src: url(./Outlined/MaterialIconsOutlined-Regular.eot); /* For IE6-8 */
  src: local('Material Icons Outlined'),
    local('MaterialIconsOutlined-Regular'),
    url(./Outlined/MaterialIconsOutlined-Regular.woff2) format('woff2'),
    url(./Outlined/MaterialIconsOutlined-Regular.woff) format('woff'),
    url(./Outlined/MaterialIconsOutlined-Regular.ttf) format('truetype');
}

@font-face {
  font-family: 'Material Icons Round';
  font-style: normal;
  font-weight: 400;
  src: url(./Rounded/MaterialIconsRound-Regular.eot); /* For IE6-8 */
  src: local('Material Icons Round'),
    local('MaterialIconsRound-Regular'),
    url(./Rounded/MaterialIconsRound-Regular.woff2) format('woff2'),
    url(./Rounded/MaterialIconsRound-Regular.woff) format('woff'),
    url(./Rounded/MaterialIconsRound-Regular.ttf) format('truetype');
}

@font-face {
  font-family: 'Material Icons Sharp';
  font-style: normal;
  font-weight: 400;
  src: url(./Sharp/MaterialIconsSharp-Regular.eot); /* For IE6-8 */
  src: local('Material Icons Sharp'),
    local('MaterialIconsSharp-Regular'),
    url(./Sharp/MaterialIconsSharp-Regular.woff2) format('woff2'),
    url(./Sharp/MaterialIconsSharp-Regular.woff) format('woff'),
    url(./Sharp/MaterialIconsSharp-Regular.ttf) format('truetype');
}

.material-icons-outlined.active {
  font-family: 'Material Icons';
}

.material-icons.active {
  font-family: 'Material Icons';
}

.material-icons {
  font-family: 'Material Icons';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;  /* Preferred icon size */
  display: inline-block;
  line-height: 1;
  text-transform: none;
  letter-spacing: normal;
  word-wrap: normal;
  white-space: nowrap;
  direction: ltr;

  /* Support for all WebKit browsers. */
  -webkit-font-smoothing: antialiased;
  /* Support for Safari and Chrome. */
  text-rendering: optimizeLegibility;

  /* Support for Firefox. */
  -moz-osx-font-smoothing: grayscale;

  /* Support for IE. */
  font-feature-settings: 'liga';
}

.material-icons-outlined {
  font-family: 'Material Icons Outlined';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

.material-icons-round {
  font-family: 'Material Icons Round';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

.material-icons-sharp {
  font-family: 'Material Icons Sharp';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

.material-icons.text-red, .material-icons-outlined.text-red, .material-icons-round.text-red, .material-icons-sharp.text-red   { color: #CC0000; }

.material-icons.text-green, .material-icons-outlined.text-green, .material-icons-round.text-green, .material-icons-sharp.text-green { color: #6EA152; }

.material-icons.text-purple, .material-icons-outlined.text-purple, .material-icons-round.text-purple, .material-icons-sharp.text-purple { color: #55588B; }

.material-icons.text-yellow, .material-icons-outlined.text-yellow, .material-icons-round.text-yellow, .material-icons-sharp.text-yellow { color: #FFD600; }

.material-icons.text-blue { color: #006EA6; }

.material-icons.text-orange { color: #FABC2A; }

