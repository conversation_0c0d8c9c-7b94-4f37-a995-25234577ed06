<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag; ?>
<?php foreach($attributes->onlyProps([
    'title',
]) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $attributes = $attributes->exceptProps([
    'title',
]); ?>
<?php foreach (array_filter(([
    'title',
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>

<head>
    <?php echo $__env->yieldPushContent('head_start'); ?>

    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8; charset=ISO-8859-1"/>

    <title><?php echo $title; ?> - <?php echo e(config('app.name')); ?></title>

    <base href="<?php echo e(config('app.url') . '/'); ?>">

    <?php if (isset($component)) { $__componentOriginal3be8359abb9f62939f8d67c521ee1b93 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal3be8359abb9f62939f8d67c521ee1b93 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layouts.pwa.head','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('layouts.pwa.head'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal3be8359abb9f62939f8d67c521ee1b93)): ?>
<?php $attributes = $__attributesOriginal3be8359abb9f62939f8d67c521ee1b93; ?>
<?php unset($__attributesOriginal3be8359abb9f62939f8d67c521ee1b93); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal3be8359abb9f62939f8d67c521ee1b93)): ?>
<?php $component = $__componentOriginal3be8359abb9f62939f8d67c521ee1b93; ?>
<?php unset($__componentOriginal3be8359abb9f62939f8d67c521ee1b93); ?>
<?php endif; ?>

    <!-- Favicon -->
    <link rel="icon" href="<?php echo e(asset('public/img/favicon.ico')); ?>" type="image/png">

    <!--Icons-->
    <link rel="stylesheet" href="<?php echo e(asset('public/css/fonts/material-icons/style.css?v=' . version('short'))); ?>" type="text/css">

     <!-- Font -->
    <link rel="stylesheet" href="<?php echo e(asset('public/vendor/quicksand/css/quicksand.css?v=' . version('short'))); ?>" type="text/css">

    <!-- Css -->
    <link rel="stylesheet" href="<?php echo e(asset('public/css//third_party/swiper-bundle.min.css?v=' . version('short'))); ?>" type="text/css">
    <link rel="stylesheet" href="<?php echo e(asset('public/css/element.css?v=' . version('short'))); ?>" type="text/css">
    <link rel="stylesheet" href="<?php echo e(asset('public/css/app.css?v=' . version('short'))); ?>" type="text/css">

    <?php echo $__env->yieldPushContent('css'); ?>

    <?php echo $__env->yieldPushContent('stylesheet'); ?>

    <script type="text/javascript"><!--
        var url = '<?php echo e(url("/")); ?>';
        var app_url = '<?php echo e(config("app.url")); ?>';
    //--></script>

    <?php echo $__env->yieldPushContent('js'); ?>

    <script type="text/javascript"><!--
        window.Laravel = <?php echo json_encode([
            'csrfToken' => csrf_token(),
        ]); ?>;

        var flash_notification = <?php echo (session()->has('flash_notification')) ? json_encode(session()->get('flash_notification')) : 'false'; ?>;
    //--></script>

    <?php echo e(session()->forget('flash_notification')); ?>


    <?php echo $__env->yieldPushContent('scripts'); ?>

    <?php echo $__env->yieldPushContent('head_end'); ?>
</head>
<?php /**PATH C:\Users\<USER>\Desktop\akaunting\resources\views/components/layouts/install/head.blade.php ENDPATH**/ ?>