@echo off
setlocal enabledelayedexpansion
color 0D

echo.
echo ========================================================
echo         AKAUNTING INSTALLATION VERIFIER
echo ========================================================
echo.

set "ERRORS=0"
set "WARNINGS=0"
set "SUCCESS=0"

echo [INFO] Verifying Akaunting installation...
echo.

:: Check 1: XAMPP Installation
echo ========================================================
echo CHECK 1: XAMPP Installation
echo ========================================================
if exist "C:\xampp\php\php.exe" (
    echo [PASS] PHP executable found
    C:\xampp\php\php.exe --version | findstr "PHP"
    set /a SUCCESS+=1
) else (
    echo [FAIL] PHP executable not found at C:\xampp\php\php.exe
    set /a ERRORS+=1
)

if exist "C:\xampp\mysql\bin\mysql.exe" (
    echo [PASS] MySQL executable found
    set /a SUCCESS+=1
) else (
    echo [FAIL] MySQL executable not found
    set /a ERRORS+=1
)

if exist "C:\xampp\apache\bin\httpd.exe" (
    echo [PASS] Apache executable found
    set /a SUCCESS+=1
) else (
    echo [FAIL] Apache executable not found
    set /a ERRORS+=1
)
echo.

:: Check 2: Composer Installation
echo ========================================================
echo CHECK 2: Composer Installation
echo ========================================================
composer --version >nul 2>&1
if !errorlevel! equ 0 (
    echo [PASS] Composer is accessible
    composer --version
    set /a SUCCESS+=1
) else (
    echo [FAIL] Composer not found in PATH
    set /a ERRORS+=1
)
echo.

:: Check 3: Akaunting Files
echo ========================================================
echo CHECK 3: Akaunting Files in XAMPP
echo ========================================================
if exist "C:\xampp\htdocs\akaunting" (
    echo [PASS] Akaunting directory found in htdocs
    set /a SUCCESS+=1
    
    if exist "C:\xampp\htdocs\akaunting\artisan" (
        echo [PASS] Laravel artisan found
        set /a SUCCESS+=1
    ) else (
        echo [FAIL] Laravel artisan not found
        set /a ERRORS+=1
    )
    
    if exist "C:\xampp\htdocs\akaunting\.env" (
        echo [PASS] Environment file found
        set /a SUCCESS+=1
    ) else (
        echo [FAIL] Environment file not found
        set /a ERRORS+=1
    )
    
    if exist "C:\xampp\htdocs\akaunting\vendor" (
        echo [PASS] Composer dependencies installed
        set /a SUCCESS+=1
    ) else (
        echo [WARN] Composer dependencies not installed
        set /a WARNINGS+=1
    )
) else (
    echo [FAIL] Akaunting not found in htdocs
    set /a ERRORS+=1
)
echo.

:: Check 4: Services Status
echo ========================================================
echo CHECK 4: Services Status
echo ========================================================
echo [INFO] Checking if Apache is running...
netstat -an | find ":80 " | find "LISTENING" >nul
if !errorlevel! equ 0 (
    echo [PASS] Apache is running on port 80
    set /a SUCCESS+=1
) else (
    echo [WARN] Apache is not running on port 80
    set /a WARNINGS+=1
)

echo [INFO] Checking if MySQL is running...
netstat -an | find ":3306 " | find "LISTENING" >nul
if !errorlevel! equ 0 (
    echo [PASS] MySQL is running on port 3306
    set /a SUCCESS+=1
) else (
    echo [WARN] MySQL is not running on port 3306
    set /a WARNINGS+=1
)
echo.

:: Check 5: Web Access Test
echo ========================================================
echo CHECK 5: Web Access Test
echo ========================================================
if !ERRORS! equ 0 (
    echo [INFO] Testing web access...
    powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost' -TimeoutSec 5; if ($response.StatusCode -eq 200) { exit 0 } else { exit 1 } } catch { exit 1 }"
    if !errorlevel! equ 0 (
        echo [PASS] Web server is responding
        set /a SUCCESS+=1
    ) else (
        echo [WARN] Web server is not responding
        set /a WARNINGS+=1
    )
) else (
    echo [SKIP] Skipping web test due to previous errors
)
echo.

:: Final Results
echo ========================================================
echo                  VERIFICATION RESULTS
echo ========================================================
echo.
echo Successful checks: !SUCCESS!
echo Warnings: !WARNINGS!
echo Errors: !ERRORS!
echo.

if !ERRORS! equ 0 (
    if !WARNINGS! equ 0 (
        echo [SUCCESS] Installation is PERFECT! 🎉
        echo [SUCCESS] Akaunting is ready to use!
        color 0A
    ) else (
        echo [SUCCESS] Installation is GOOD with minor issues
        echo [INFO] Some services may need to be started manually
        color 0E
    )
    
    echo.
    echo NEXT STEPS:
    echo 1. Start XAMPP Control Panel: C:\xampp\xampp-control.exe
    echo 2. Start Apache and MySQL services
    echo 3. Open phpMyAdmin: http://localhost/phpmyadmin
    echo 4. Create 'akaunting' database
    echo 5. Run migrations: cd C:\xampp\htdocs\akaunting ^&^& php artisan migrate --seed
    echo 6. Open Akaunting: http://localhost/akaunting
    
) else (
    echo [ERROR] Installation has CRITICAL ISSUES! ❌
    echo [ERROR] Please review the failed checks above
    color 0C
    
    echo.
    echo TROUBLESHOOTING:
    echo - If XAMPP not found: Run install-xampp.bat as Administrator
    echo - If Composer not found: Install from https://getcomposer.org/
    echo - If files missing: Run setup-akaunting.bat as Administrator
)

echo.
echo ========================================================

if !ERRORS! equ 0 (
    echo [INFO] Opening XAMPP Control Panel...
    if exist "C:\xampp\xampp-control.exe" (
        start "" "C:\xampp\xampp-control.exe"
    )
    
    echo [INFO] Opening Akaunting in browser...
    timeout /t 3 /nobreak >nul
    start "" "http://localhost/akaunting"
)

echo.
echo Verification completed. Press any key to exit...
pause >nul
