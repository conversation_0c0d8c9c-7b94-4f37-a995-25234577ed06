<!-- Web Application Manifest -->
<link rel="manifest" href="<?php echo e(asset('manifest.json')); ?>">

<!-- Chrome for Android theme color -->
<meta name="theme-color" content="#ffffff">

<!-- Add to homescreen for Chrome on Android -->
<meta name="mobile-web-app-capable" content="yes">
<meta name="application-name" content="<?php echo e(config('app.name')); ?>">
<link rel="icon" sizes="512x512" href="<?php echo e(asset('public/img/pwa/icon-512x512.png')); ?>">

<!-- Add to homescreen for Safari on iOS -->
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="#ffffff">
<meta name="apple-mobile-web-app-title" content="<?php echo e(config('app.name')); ?>">
<link rel="apple-touch-icon" href="<?php echo e(asset('public/img/pwa/icon-512x512.png')); ?>">

<link href="<?php echo e(asset('public/img/pwa/splash-640x1136.png')); ?>" media="(device-width: 320px) and (device-height: 568px) and (-webkit-device-pixel-ratio: 2)" rel="apple-touch-startup-image" />
<link href="<?php echo e(asset('public/img/pwa/splash-750x1334.png')); ?>" media="(device-width: 375px) and (device-height: 667px) and (-webkit-device-pixel-ratio: 2)" rel="apple-touch-startup-image" />
<link href="<?php echo e(asset('public/img/pwa/splash-828x1792.png')); ?>" media="(device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 2)" rel="apple-touch-startup-image" />
<link href="<?php echo e(asset('public/img/pwa/splash-1242x2208.png')); ?>" media="(device-width: 621px) and (device-height: 1104px) and (-webkit-device-pixel-ratio: 3)" rel="apple-touch-startup-image" />
<link href="<?php echo e(asset('public/img/pwa/splash-1242x2688.png')); ?>" media="(device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 3)" rel="apple-touch-startup-image" />
<link href="<?php echo e(asset('public/img/pwa/splash-1536x2048.png')); ?>" media="(device-width: 768px) and (device-height: 1024px) and (-webkit-device-pixel-ratio: 2)" rel="apple-touch-startup-image" />
<link href="<?php echo e(asset('public/img/pwa/splash-1668x2224.png')); ?>" media="(device-width: 834px) and (device-height: 1112px) and (-webkit-device-pixel-ratio: 2)" rel="apple-touch-startup-image" />
<link href="<?php echo e(asset('public/img/pwa/splash-1668x2388.png')); ?>" media="(device-width: 834px) and (device-height: 1194px) and (-webkit-device-pixel-ratio: 2)" rel="apple-touch-startup-image" />
<link href="<?php echo e(asset('public/img/pwa/splash-2048x2732.png')); ?>" media="(device-width: 1024px) and (device-height: 1366px) and (-webkit-device-pixel-ratio: 2)" rel="apple-touch-startup-image" />

<!-- Tile for Win8 -->
<meta name="msapplication-TileColor" content="#ffffff">
<meta name="msapplication-TileImage" content="<?php echo e(asset('public/img/pwa/icon-512x512.png')); ?>">

<script type="text/javascript">
    if ('serviceWorker' in navigator) {
        navigator.serviceWorker.register("<?php echo e(asset('serviceworker.js')); ?>");
    }
</script>
<?php /**PATH C:\Users\<USER>\Desktop\akaunting\resources\views/components/layouts/pwa/head.blade.php ENDPATH**/ ?>