@echo off
setlocal enabledelayedexpansion
color 0A

echo.
echo ========================================================
echo           STARTING AKAUNTING PROJECT
echo ========================================================
echo.

:: Step 1: Verify installations
echo [INFO] Verifying installations...

:: Check PHP
if exist "C:\xampp\php\php.exe" (
    echo [SUCCESS] PHP found at C:\xampp\php\php.exe
    set "PATH=C:\xampp\php;%PATH%"
) else (
    echo [ERROR] PHP not found. Please install XAMPP first.
    pause
    exit /b 1
)

:: Check Composer
composer --version >nul 2>&1
if %errorlevel% equ 0 (
    echo [SUCCESS] Composer is available
) else (
    echo [ERROR] Composer not found. Please install Composer first.
    pause
    exit /b 1
)

echo.

:: Step 2: Install dependencies
echo ========================================================
echo STEP 1: Installing Dependencies
echo ========================================================
echo [INFO] Installing Composer dependencies...
composer install --no-dev --optimize-autoloader --no-interaction

if %errorlevel% neq 0 (
    echo [ERROR] Failed to install dependencies
    pause
    exit /b 1
)

echo [SUCCESS] Dependencies installed successfully!
echo.

:: Step 3: Generate application key
echo ========================================================
echo STEP 2: Generating Application Key
echo ========================================================
echo [INFO] Generating Laravel application key...
php artisan key:generate --no-interaction

if %errorlevel% neq 0 (
    echo [ERROR] Failed to generate application key
    pause
    exit /b 1
)

echo [SUCCESS] Application key generated!
echo.

:: Step 4: Set up storage directories
echo ========================================================
echo STEP 3: Setting Up Storage Directories
echo ========================================================
echo [INFO] Creating storage directories...

if not exist "storage\logs" mkdir "storage\logs"
if not exist "storage\framework\cache" mkdir "storage\framework\cache"
if not exist "storage\framework\sessions" mkdir "storage\framework\sessions"
if not exist "storage\framework\views" mkdir "storage\framework\views"
if not exist "storage\app\public" mkdir "storage\app\public"
if not exist "bootstrap\cache" mkdir "bootstrap\cache"

echo [SUCCESS] Storage directories created!
echo.

:: Step 5: Copy to XAMPP htdocs (if not already there)
echo ========================================================
echo STEP 4: Setting Up Web Directory
echo ========================================================

set "CURRENT_DIR=%cd%"
set "HTDOCS_PATH=C:\xampp\htdocs\akaunting"

if /i "%CURRENT_DIR%" neq "%HTDOCS_PATH%" (
    echo [INFO] Copying files to XAMPP htdocs...
    if not exist "C:\xampp\htdocs" mkdir "C:\xampp\htdocs"
    
    if exist "%HTDOCS_PATH%" (
        echo [WARNING] Akaunting directory already exists in htdocs
        echo [INFO] Updating files...
        xcopy "%CURRENT_DIR%\*" "%HTDOCS_PATH%\" /E /Y /H
    ) else (
        echo [INFO] Creating new installation in htdocs...
        xcopy "%CURRENT_DIR%\*" "%HTDOCS_PATH%\" /E /I /H /Y
    )
    
    echo [SUCCESS] Files copied to %HTDOCS_PATH%
    echo [INFO] Changing to htdocs directory...
    cd /d "%HTDOCS_PATH%"
) else (
    echo [INFO] Already in XAMPP htdocs directory
)

echo.

:: Step 6: Start XAMPP services
echo ========================================================
echo STEP 5: Starting XAMPP Services
echo ========================================================

echo [INFO] Checking XAMPP Control Panel...
if exist "C:\xampp\xampp-control.exe" (
    echo [INFO] Starting XAMPP Control Panel...
    start "" "C:\xampp\xampp-control.exe"
    echo [SUCCESS] XAMPP Control Panel opened
    echo.
    echo [INFO] Please start Apache and MySQL services in the control panel
    echo [INFO] Press any key after starting the services...
    pause >nul
) else (
    echo [WARNING] XAMPP Control Panel not found
    echo [INFO] Please start XAMPP services manually
)

echo.

:: Step 7: Check services
echo ========================================================
echo STEP 6: Verifying Services
echo ========================================================

echo [INFO] Checking if Apache is running...
netstat -an | find ":80 " | find "LISTENING" >nul
if %errorlevel% equ 0 (
    echo [SUCCESS] Apache is running on port 80
) else (
    echo [WARNING] Apache is not running. Please start it in XAMPP Control Panel
)

echo [INFO] Checking if MySQL is running...
netstat -an | find ":3306 " | find "LISTENING" >nul
if %errorlevel% equ 0 (
    echo [SUCCESS] MySQL is running on port 3306
) else (
    echo [WARNING] MySQL is not running. Please start it in XAMPP Control Panel
)

echo.

:: Step 8: Database setup instructions
echo ========================================================
echo STEP 7: Database Setup
echo ========================================================
echo.
echo [INFO] Next steps for database setup:
echo 1. Open phpMyAdmin: http://localhost/phpmyadmin
echo 2. Create a new database named 'akaunting'
echo 3. Run migrations with: php artisan migrate --seed
echo.

:: Step 9: Start development server
echo ========================================================
echo STEP 8: Starting Development Server
echo ========================================================
echo.
echo [INFO] You can now access Akaunting in two ways:
echo.
echo Option 1 - Via XAMPP (Recommended):
echo   URL: http://localhost/akaunting
echo.
echo Option 2 - Via Laravel development server:
echo   Command: php artisan serve
echo   URL: http://localhost:8000
echo.

echo [INFO] Do you want to start the Laravel development server? (Y/N)
set /p "startServer=Enter choice: "

if /i "!startServer!" equ "Y" (
    echo [INFO] Starting Laravel development server...
    echo [INFO] Press Ctrl+C to stop the server
    echo.
    php artisan serve
) else (
    echo [INFO] You can start the server later with: php artisan serve
)

echo.
echo ========================================================
echo              PROJECT SETUP COMPLETE!
echo ========================================================
echo.
echo NEXT STEPS:
echo 1. Ensure Apache and MySQL are running in XAMPP
echo 2. Create 'akaunting' database in phpMyAdmin
echo 3. Run: php artisan migrate --seed
echo 4. Open: http://localhost/akaunting
echo 5. Complete the web setup wizard
echo.
echo USEFUL COMMANDS:
echo - Start dev server: php artisan serve
echo - Run migrations: php artisan migrate --seed
echo - Clear cache: php artisan cache:clear
echo - View logs: type storage\logs\laravel.log
echo.

pause
