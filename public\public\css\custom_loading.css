@media (min-width: 1024px) {
  .lg\:w-4\/5 {
    width: 80%;
  }

  .lg\:-mt-16 {
    margin-top: -4rem;
  }

  .lg\:flex {
    display: flex;
  }
}

[dir="ltr"] .ltr\:right-0 {
  right: 0px;
}

[dir="rtl"] .ltr\:left-0 {
  left: 0px;
}

.bg-body {
  background-color: #fcfcfc;
}

.justify-center {
  justify-content: center;
}

.items-center {
  align-items: center;
}

.items-start {
  align-items: flex-start;
}

.w-full {
  width: 100%;
}

.h-screen {
  height: 100vh;
}

.-mx-1 {
  margin-left: -0.25rem;
  margin-right: -0.25rem;
}

.bottom-0 {
  bottom: 0px;
}

.top-0 {
  top: 0px;
}

.left-0 {
  left: 0px;
}

.right-0 {
  right: 0px;
}

.fixed {
  position: fixed;
}

.absolute {
  position: absolute;
}

.w-28 {
  width: 7rem;
}

.h-28 {
    height: 7rem;
}

.material-icons-outlined {
  font-family: 'Material Icons Outlined';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
  }
  
