<input type="text"
    name="<?php echo e($name); ?>"
    id="<?php echo e($id); ?>"
    class="w-full text-sm px-3 py-2.5 mt-1 rounded-lg border border-light-gray text-black placeholder-light-gray bg-white disabled:bg-gray-200 focus:outline-none focus:ring-transparent focus:border-purple"
    value="<?php echo $value; ?>"
    placeholder="<?php echo $placeholder; ?>"
    <?php if($disabled): ?>
    disabled="disabled"
    <?php endif; ?>
    <?php if($required): ?>
    required="required"
    <?php endif; ?>
    <?php if($readonly): ?>
    readonly="readonly"
    <?php endif; ?>
    <?php echo e($attributes->except(['placeholder', 'disabled', 'required', 'readonly', 'v-error', 'v-error-message'])); ?>

/><?php /**PATH C:\Users\<USER>\Desktop\akaunting\resources\views/components/form/input/text.blade.php ENDPATH**/ ?>