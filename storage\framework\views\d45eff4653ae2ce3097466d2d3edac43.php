<?php echo $__env->yieldPushContent($name . '_input_start'); ?>
    <div
        class="<?php echo \Illuminate\Support\Arr::toCssClasses([
            'relative',
            $formGroupClass,
            'required' => $required,
            'readonly' =>  $readonly,
            'disabled' => $disabled,
        ]); ?>"

        <?php if(isset($attributes['v-show'])): ?>
        v-if="<?php echo e($attributes['v-show']); ?>"
        <?php endif; ?>

        <?php if(isset($attributes['v-disabled'])): ?>
        :class="[
            {'disabled' : <?php echo e($attributes['v-disabled']); ?>},
            {'has-error': <?php echo e(isset($attributes['v-error']) ? $attributes['v-error'] : 'form.errors.get("' . $name . '")'); ?>}
        ]"
        <?php else: ?>
        :class="[
            {'has-error': <?php echo e(isset($attributes['v-error']) ? $attributes['v-error'] : 'form.errors.get("' . $name . '")'); ?>}
        ]"
        <?php endif; ?>
    >
        <?php if(! $attributes->has('label') && ! empty($label->contents)): ?>
            <?php echo $label ?? ''; ?>

        <?php elseif(! empty($label)): ?>
            <?php if (isset($component)) { $__componentOriginal306f477fe089d4f950325a3d0a498c1c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal306f477fe089d4f950325a3d0a498c1c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.label','data' => ['for' => ''.e($name).'','required' => $required]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.label'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['for' => ''.e($name).'','required' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($required)]); ?><?php echo $label; ?> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal306f477fe089d4f950325a3d0a498c1c)): ?>
<?php $attributes = $__attributesOriginal306f477fe089d4f950325a3d0a498c1c; ?>
<?php unset($__attributesOriginal306f477fe089d4f950325a3d0a498c1c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal306f477fe089d4f950325a3d0a498c1c)): ?>
<?php $component = $__componentOriginal306f477fe089d4f950325a3d0a498c1c; ?>
<?php unset($__componentOriginal306f477fe089d4f950325a3d0a498c1c); ?>
<?php endif; ?>
        <?php endif; ?>

        <div class="<?php echo \Illuminate\Support\Arr::toCssClasses([
                $inputGroupClass,
            ]); ?>"
        >
            <?php if(! $attributes->has('icon') && ! empty($icon->contents)): ?>
                <?php echo $icon ?? ''; ?>

            <?php elseif(! empty($icon)): ?>
                <?php if (isset($component)) { $__componentOriginal9791e0b679eecbc88c7e2a2e321623af = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9791e0b679eecbc88c7e2a2e321623af = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.icon','data' => ['icon' => ''.e($icon).'']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => ''.e($icon).'']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9791e0b679eecbc88c7e2a2e321623af)): ?>
<?php $attributes = $__attributesOriginal9791e0b679eecbc88c7e2a2e321623af; ?>
<?php unset($__attributesOriginal9791e0b679eecbc88c7e2a2e321623af); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9791e0b679eecbc88c7e2a2e321623af)): ?>
<?php $component = $__componentOriginal9791e0b679eecbc88c7e2a2e321623af; ?>
<?php unset($__componentOriginal9791e0b679eecbc88c7e2a2e321623af); ?>
<?php endif; ?>
            <?php endif; ?>

            <?php if (isset($component)) { $__componentOriginal8d9e85e6aa3d84f0c6a777cc1af58e80 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8d9e85e6aa3d84f0c6a777cc1af58e80 = $attributes; } ?>
<?php $component = App\View\Components\Form\Input\Password::resolve(['name' => ''.e($name).'','id' => ''.e($id).'','value' => ''.e($value).'','placeholder' => ''.e($placeholder).''] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.input.password'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\Form\Input\Password::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['v-model' => ''.e(!empty($attributes['v-model']) ? $attributes['v-model'] : (!empty($attributes['data-field']) ? 'form.' . $attributes['data-field'] . '.' . $name : 'form.' . $name)).'','attributes' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($attributes->merge($custom_attributes))]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8d9e85e6aa3d84f0c6a777cc1af58e80)): ?>
<?php $attributes = $__attributesOriginal8d9e85e6aa3d84f0c6a777cc1af58e80; ?>
<?php unset($__attributesOriginal8d9e85e6aa3d84f0c6a777cc1af58e80); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8d9e85e6aa3d84f0c6a777cc1af58e80)): ?>
<?php $component = $__componentOriginal8d9e85e6aa3d84f0c6a777cc1af58e80; ?>
<?php unset($__componentOriginal8d9e85e6aa3d84f0c6a777cc1af58e80); ?>
<?php endif; ?>
        </div>

        <?php if(! $attributes->has('error') && ! empty($error->contents)): ?>
            <?php echo $error ?? ''; ?>

        <?php else: ?>
            <?php if (isset($component)) { $__componentOriginal8a61cf4ce6144d9e2012fbc98db0155f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8a61cf4ce6144d9e2012fbc98db0155f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.error','data' => ['name' => ''.e($name).'','attributes' => $attributes]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.error'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => ''.e($name).'','attributes' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($attributes)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8a61cf4ce6144d9e2012fbc98db0155f)): ?>
<?php $attributes = $__attributesOriginal8a61cf4ce6144d9e2012fbc98db0155f; ?>
<?php unset($__attributesOriginal8a61cf4ce6144d9e2012fbc98db0155f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8a61cf4ce6144d9e2012fbc98db0155f)): ?>
<?php $component = $__componentOriginal8a61cf4ce6144d9e2012fbc98db0155f; ?>
<?php unset($__componentOriginal8a61cf4ce6144d9e2012fbc98db0155f); ?>
<?php endif; ?>
        <?php endif; ?>
    </div>
<?php echo $__env->yieldPushContent($name . '_input_end'); ?>
<?php /**PATH C:\Users\<USER>\Desktop\akaunting\resources\views/components/form/group/password.blade.php ENDPATH**/ ?>